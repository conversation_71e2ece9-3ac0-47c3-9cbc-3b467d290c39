<?php
/**
 * Test Payment System
 * Quick test page to verify all payment features are working
 */

session_start();
require_once 'config/database.php';

// Auto-login as admin for testing
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['role'] = 'admin';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get quick statistics
$stats_query = "SELECT
                COUNT(DISTINCT pt.member_id) as total_paying_members,
                COUNT(pt.id) as total_transactions,
                SUM(pt.amount) as total_collected,
                AVG(pt.amount) as avg_payment
                FROM payment_transactions pt
                WHERE pt.status = 'completed'";

$stats = $db->query($stats_query)->fetch();

// Get payment method breakdown
$methods_query = "SELECT
                    payment_method,
                    COUNT(*) as count,
                    SUM(amount) as total
                  FROM payment_transactions
                  WHERE status = 'completed'
                  GROUP BY payment_method
                  ORDER BY count DESC";

$methods = $db->query($methods_query)->fetchAll();

// Get recent transactions
$recent_query = "SELECT
                    pt.receipt_number,
                    pt.amount,
                    pt.payment_method,
                    pt.transaction_date,
                    CONCAT(m.first_name, ' ', m.last_name) as member_name
                 FROM payment_transactions pt
                 JOIN members m ON pt.member_id = m.id
                 WHERE pt.status = 'completed'
                 ORDER BY pt.transaction_date DESC
                 LIMIT 10";

$recent_transactions = $db->query($recent_query)->fetchAll();

function formatCurrency($amount) {
    return '₱' . number_format($amount ?? 0, 2);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment System Test - LMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .feature-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease-in-out;
        }
        .feature-card:hover {
            transform: translateY(-2px);
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <!-- Header -->
        <div class="text-center mb-5">
            <h1 class="display-4 mb-3">🎉 Payment System Successfully Generated!</h1>
            <p class="lead text-muted">Your Library Management System now has a fully functional payment system with sample data.</p>
        </div>

        <!-- Statistics Overview -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card stats-card">
                    <div class="card-body">
                        <h3 class="text-center mb-4">📊 Payment Statistics Overview</h3>
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h2 class="mb-1"><?php echo $stats['total_paying_members'] ?? 0; ?></h2>
                                <p class="mb-0">Paying Members</p>
                            </div>
                            <div class="col-md-3">
                                <h2 class="mb-1"><?php echo $stats['total_transactions'] ?? 0; ?></h2>
                                <p class="mb-0">Total Transactions</p>
                            </div>
                            <div class="col-md-3">
                                <h2 class="mb-1"><?php echo formatCurrency($stats['total_collected']); ?></h2>
                                <p class="mb-0">Total Collected</p>
                            </div>
                            <div class="col-md-3">
                                <h2 class="mb-1"><?php echo formatCurrency($stats['avg_payment']); ?></h2>
                                <p class="mb-0">Average Payment</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Methods Breakdown -->
        <div class="row mb-5">
            <div class="col-md-6">
                <div class="card feature-card">
                    <div class="card-header">
                        <h5 class="mb-0">💳 Payment Methods Used</h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($methods as $method): ?>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span><?php echo ucfirst(str_replace('_', ' ', $method['payment_method'])); ?></span>
                                <div>
                                    <span class="badge bg-primary me-2"><?php echo $method['count']; ?></span>
                                    <strong><?php echo formatCurrency($method['total']); ?></strong>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card feature-card">
                    <div class="card-header">
                        <h5 class="mb-0">🔗 Available Features</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <a href="admin/payment_reports.php" class="list-group-item list-group-item-action">
                                <i class="bi bi-file-earmark-bar-graph me-2"></i>Payment Reports Dashboard
                            </a>
                            <a href="admin/payment_processing.php" class="list-group-item list-group-item-action">
                                <i class="bi bi-credit-card me-2"></i>Payment Processing
                            </a>
                            <a href="admin/financial_management.php" class="list-group-item list-group-item-action">
                                <i class="bi bi-gear me-2"></i>Financial Management
                            </a>
                            <a href="member/pay_fine.php" class="list-group-item list-group-item-action">
                                <i class="bi bi-person-check me-2"></i>Member Payment Portal
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card feature-card">
                    <div class="card-header">
                        <h5 class="mb-0">🕒 Recent Transactions</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Receipt #</th>
                                        <th>Member</th>
                                        <th>Amount</th>
                                        <th>Method</th>
                                        <th>Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($recent_transactions, 0, 5) as $transaction): ?>
                                        <tr>
                                            <td><code><?php echo htmlspecialchars($transaction['receipt_number']); ?></code></td>
                                            <td><?php echo htmlspecialchars($transaction['member_name']); ?></td>
                                            <td><strong class="text-success"><?php echo formatCurrency($transaction['amount']); ?></strong></td>
                                            <td><?php echo ucfirst(str_replace('_', ' ', $transaction['payment_method'])); ?></td>
                                            <td><?php echo date('M j, Y g:i A', strtotime($transaction['transaction_date'])); ?></td>
                                            <td>
                                                <a href="admin/payment_receipt.php?receipt=<?php echo $transaction['receipt_number']; ?>" 
                                                   class="btn btn-sm btn-outline-primary" target="_blank">
                                                    <i class="bi bi-receipt"></i> Receipt
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center">
            <h4 class="mb-4">🚀 Ready to Use!</h4>
            <div class="btn-group" role="group">
                <a href="admin/payment_reports.php" class="btn btn-primary btn-lg">
                    <i class="bi bi-graph-up me-2"></i>View Payment Reports
                </a>
                <a href="admin_dashboard.php" class="btn btn-outline-primary btn-lg">
                    <i class="bi bi-speedometer2 me-2"></i>Go to Dashboard
                </a>
                <a href="admin/payment_processing.php" class="btn btn-success btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>Process New Payment
                </a>
            </div>
        </div>

        <!-- Success Message -->
        <div class="alert alert-success mt-5" role="alert">
            <h4 class="alert-heading">✅ System Ready!</h4>
            <p>Your payment system has been successfully set up with:</p>
            <ul class="mb-0">
                <li><strong><?php echo count($recent_transactions); ?></strong> sample payment transactions</li>
                <li><strong>6</strong> different payment methods (Cash, GCash, PayMaya, Bank Transfer, Credit Card, Debit Card)</li>
                <li><strong>10</strong> sample members with realistic Filipino names</li>
                <li><strong>10</strong> sample Filipino literature books</li>
                <li>Complete payment reports with filtering and export capabilities</li>
                <li>Individual member payment history tracking</li>
                <li>Printable payment receipts</li>
                <li>Payment processing interface for staff</li>
            </ul>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
