<?php
/**
 * Library Management System Setup Script
 *
 * This script helps to set up the LMS database and initial configuration.
 */

// Configuration
$db_host = 'localhost';
$db_name = 'lms_db';
$db_user = 'root';
$db_pass = 'password1234';

// Initialize variables
$success = false;
$error = '';
$message = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_name = $_POST['db_name'] ?? 'lms_db';
    $db_user = $_POST['db_user'] ?? 'root';
    $db_pass = $_POST['db_pass'] ?? 'password1234';

    try {
        // Connect to MySQL server (without selecting a database)
        $conn = new PDO("mysql:host=$db_host", $db_user, $db_pass);
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Check if database exists, if not create it
        $conn->exec("CREATE DATABASE IF NOT EXISTS `$db_name` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $conn->exec("USE `$db_name`");

        // Read SQL file
        $sql_file = file_get_contents('database/lms_db.sql');

        if (!$sql_file) {
            throw new Exception("Could not read SQL file");
        }

        // Split SQL commands and execute them one by one
        $sql_commands = explode(';', $sql_file);

        foreach ($sql_commands as $command) {
            $command = trim($command);
            if (!empty($command) && !preg_match('/^--/', $command)) {
                try {
                    $conn->exec($command);
                } catch (PDOException $e) {
                    // Log the error but continue with other commands
                    error_log("SQL Error: " . $e->getMessage() . " for command: " . $command);
                }
            }
        }

        // Update database configuration file
        $config_file = 'config/database.php';
        $config_content = file_get_contents($config_file);

        // Replace database connection details
        $config_content = preg_replace('/private \$host = ".*?";/', 'private $host = "' . $db_host . '";', $config_content);
        $config_content = preg_replace('/private \$db_name = ".*?";/', 'private $db_name = "' . $db_name . '";', $config_content);
        $config_content = preg_replace('/private \$username = ".*?";/', 'private $username = "' . $db_user . '";', $config_content);
        $config_content = preg_replace('/private \$password = ".*?";/', 'private $password = "' . $db_pass . '";', $config_content);

        file_put_contents($config_file, $config_content);

        // Create necessary directories
        $directories = [
            'uploads',
            'uploads/covers',
            'uploads/members',
            'backups'
        ];

        foreach ($directories as $dir) {
            if (!file_exists($dir)) {
                mkdir($dir, 0777, true);
            }
        }

        // Create additional tables for enhanced dashboard
        try {
            // Create notifications table
            $conn->exec("CREATE TABLE IF NOT EXISTS `notifications` (
                `id` INT(11) NOT NULL AUTO_INCREMENT,
                `user_id` INT(11) DEFAULT NULL,
                `message` TEXT NOT NULL,
                `type` VARCHAR(50) NOT NULL DEFAULT 'info',
                `is_read` TINYINT(1) NOT NULL DEFAULT 0,
                `entity_type` VARCHAR(50) DEFAULT NULL,
                `entity_id` INT(11) DEFAULT NULL,
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `idx_user_id` (`user_id`),
                KEY `idx_is_read` (`is_read`),
                KEY `idx_created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

            // Create activity_log table
            $conn->exec("CREATE TABLE IF NOT EXISTS `activity_log` (
                `id` INT(11) NOT NULL AUTO_INCREMENT,
                `user_id` INT(11) DEFAULT NULL,
                `action` VARCHAR(50) NOT NULL,
                `description` TEXT NOT NULL,
                `entity_type` VARCHAR(50) DEFAULT NULL,
                `entity_id` INT(11) DEFAULT NULL,
                `ip_address` VARCHAR(45) DEFAULT NULL,
                `user_agent` TEXT DEFAULT NULL,
                `timestamp` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `idx_user_id` (`user_id`),
                KEY `idx_action` (`action`),
                KEY `idx_timestamp` (`timestamp`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

            // Add sample notifications
            $sample_notifications = [
                [
                    'message' => 'Welcome to the new Library Management System dashboard!',
                    'type' => 'info'
                ],
                [
                    'message' => 'There are books that need to be returned today. Please check the overdue books report.',
                    'type' => 'warning',
                    'entity_type' => 'report',
                    'entity_id' => 1
                ],
                [
                    'message' => 'New feature: You can now export reports to Excel and PDF formats.',
                    'type' => 'info'
                ]
            ];

            $stmt = $conn->prepare("INSERT INTO notifications (user_id, message, type, entity_type, entity_id) VALUES (NULL, :message, :type, :entity_type, :entity_id)");

            foreach ($sample_notifications as $notification) {
                $stmt->bindValue(':message', $notification['message']);
                $stmt->bindValue(':type', $notification['type']);
                $stmt->bindValue(':entity_type', $notification['entity_type'] ?? NULL);
                $stmt->bindValue(':entity_id', $notification['entity_id'] ?? NULL);
                $stmt->execute();
            }

            // Add sample activity logs
            $sample_activities = [
                [
                    'user_id' => 1, // Admin user
                    'action' => 'login',
                    'description' => 'Admin logged in to the system',
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
                ],
                [
                    'user_id' => 1,
                    'action' => 'setup',
                    'description' => 'System setup completed',
                    'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
                ]
            ];

            $stmt = $conn->prepare("INSERT INTO activity_log (user_id, action, description, ip_address) VALUES (:user_id, :action, :description, :ip_address)");

            foreach ($sample_activities as $activity) {
                $stmt->bindValue(':user_id', $activity['user_id']);
                $stmt->bindValue(':action', $activity['action']);
                $stmt->bindValue(':description', $activity['description']);
                $stmt->bindValue(':ip_address', $activity['ip_address']);
                $stmt->execute();
            }
        } catch (PDOException $e) {
            // Log error but continue setup
            error_log("Error creating additional tables: " . $e->getMessage());
        }

        $success = true;
        $message = "Library Management System has been successfully set up with enhanced dashboard features!";
    } catch (PDOException $e) {
        $error = "Database Error: " . $e->getMessage();
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .setup-container {
            max-width: 600px;
            margin: 50px auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .card-header {
            background-color: #343a40;
            color: white;
            font-weight: bold;
        }
        .btn-primary {
            background-color: #343a40;
            border-color: #343a40;
        }
        .btn-primary:hover {
            background-color: #23272b;
            border-color: #23272b;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="card">
            <div class="card-header text-center py-3">
                <h4 class="mb-0"><i class="bi bi-book me-2"></i>Library Management System Setup</h4>
            </div>
            <div class="card-body p-4">
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <h5><i class="bi bi-check-circle-fill me-2"></i>Setup Completed!</h5>
                        <p><?php echo $message; ?></p>
                        <hr>
                        <p>You can now <a href="index.php" class="alert-link">login to the system</a> with the following credentials:</p>
                        <ul>
                            <li><strong>Username:</strong> admin</li>
                            <li><strong>Password:</strong> admin123</li>
                        </ul>
                        <p>Please change the default password after your first login for security reasons.</p>
                        <p class="mb-0">Your dashboard now includes enhanced features like notifications, activity logging, system health monitoring, and more!</p>
                    </div>
                    <div class="text-center mt-3">
                        <a href="index.php" class="btn btn-primary">Go to Login Page</a>
                    </div>
                <?php else: ?>
                    <?php if (!empty($error)): ?>
                        <div class="alert alert-danger">
                            <?php echo $error; ?>
                        </div>
                    <?php endif; ?>

                    <p class="mb-4">This wizard will help you set up the Library Management System database and initial configuration.</p>

                    <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                        <div class="mb-3">
                            <label for="db_host" class="form-label">Database Host</label>
                            <input type="text" class="form-control" id="db_host" name="db_host" value="<?php echo $db_host; ?>" required>
                            <div class="form-text">Usually "localhost" for local development.</div>
                        </div>

                        <div class="mb-3">
                            <label for="db_name" class="form-label">Database Name</label>
                            <input type="text" class="form-control" id="db_name" name="db_name" value="<?php echo $db_name; ?>" required>
                            <div class="form-text">The database will be created if it doesn't exist.</div>
                        </div>

                        <div class="mb-3">
                            <label for="db_user" class="form-label">Database Username</label>
                            <input type="text" class="form-control" id="db_user" name="db_user" value="<?php echo $db_user; ?>" required>
                            <div class="form-text">Usually "root" for local development.</div>
                        </div>

                        <div class="mb-3">
                            <label for="db_pass" class="form-label">Database Password</label>
                            <input type="password" class="form-control" id="db_pass" name="db_pass" value="<?php echo $db_pass; ?>">
                            <div class="form-text">Leave empty if no password is set (common in local development).</div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Set Up Database</button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0">Library Management System &copy; <?php echo date('Y'); ?></p>
            </div>
        </div>
    </div>
</body>
</html>
