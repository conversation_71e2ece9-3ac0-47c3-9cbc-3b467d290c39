<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fix Book Covers Display</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2><i class='bi bi-image'></i> Fix Book Covers Display</h2>";

$action = $_GET['action'] ?? 'check';

if ($action === 'fix') {
    echo "<div class='alert alert-info'><strong>Fixing book cover display issues...</strong></div>";
    
    // Get all available cover files
    $covers_dir = 'uploads/covers/';
    $available_covers = [];
    if (is_dir($covers_dir)) {
        $files = scandir($covers_dir);
        foreach ($files as $file) {
            if ($file != '.' && $file != '..' && in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])) {
                $available_covers[] = $file;
            }
        }
    }
    
    echo "<div class='alert alert-success'>Found " . count($available_covers) . " cover images in uploads/covers/</div>";
    
    // Get all books
    $query = "SELECT id, title, author, cover_image FROM books ORDER BY id";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $books = $stmt->fetchAll();
    
    $fixed_count = 0;
    $assigned_count = 0;
    
    foreach ($books as $book) {
        $needs_update = false;
        $new_cover = $book['cover_image'];
        
        // If book has no cover, assign one from available covers
        if (empty($book['cover_image'])) {
            if (!empty($available_covers)) {
                $new_cover = $available_covers[$assigned_count % count($available_covers)];
                $needs_update = true;
                $assigned_count++;
                echo "<div class='alert alert-info'>📚 Assigning cover '{$new_cover}' to '{$book['title']}'</div>";
            }
        } else {
            // Check if the current cover file exists
            $current_path = $covers_dir . basename($book['cover_image']);
            if (!file_exists($current_path)) {
                // Try to find a matching cover or assign a new one
                $title_words = explode(' ', strtolower($book['title']));
                $found_match = false;
                
                foreach ($available_covers as $cover) {
                    $cover_lower = strtolower($cover);
                    foreach ($title_words as $word) {
                        if (strlen($word) > 3 && strpos($cover_lower, $word) !== false) {
                            $new_cover = $cover;
                            $needs_update = true;
                            $found_match = true;
                            echo "<div class='alert alert-success'>🎯 Found matching cover '{$cover}' for '{$book['title']}'</div>";
                            break 2;
                        }
                    }
                }
                
                if (!$found_match && !empty($available_covers)) {
                    $new_cover = $available_covers[$assigned_count % count($available_covers)];
                    $needs_update = true;
                    $assigned_count++;
                    echo "<div class='alert alert-warning'>🔄 Reassigning cover '{$new_cover}' to '{$book['title']}'</div>";
                }
            } else {
                // File exists, but make sure the database has the correct filename
                $correct_filename = basename($book['cover_image']);
                if ($book['cover_image'] !== $correct_filename) {
                    $new_cover = $correct_filename;
                    $needs_update = true;
                    echo "<div class='alert alert-info'>🔧 Fixing filename for '{$book['title']}': '{$book['cover_image']}' → '{$correct_filename}'</div>";
                }
            }
        }
        
        // Update the database if needed
        if ($needs_update) {
            $update_query = "UPDATE books SET cover_image = :cover_image WHERE id = :id";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->bindParam(':cover_image', $new_cover);
            $update_stmt->bindParam(':id', $book['id']);
            
            if ($update_stmt->execute()) {
                $fixed_count++;
            }
        }
    }
    
    echo "<div class='alert alert-success'><strong>✅ Fixed {$fixed_count} book cover entries!</strong></div>";
    echo "<div class='mt-3'>";
    echo "<a href='catalog.php' class='btn btn-primary btn-lg me-2'>View Catalog</a>";
    echo "<a href='fix_book_covers_display.php' class='btn btn-secondary'>Check Status Again</a>";
    echo "</div>";
    
} else {
    // Check current status
    echo "<h3>Current Status Check</h3>";
    
    // Check covers directory
    $covers_dir = 'uploads/covers/';
    if (!is_dir($covers_dir)) {
        echo "<div class='alert alert-danger'>❌ Covers directory 'uploads/covers/' does not exist!</div>";
    } else {
        $files = scandir($covers_dir);
        $cover_files = [];
        foreach ($files as $file) {
            if ($file != '.' && $file != '..' && in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif'])) {
                $cover_files[] = $file;
            }
        }
        echo "<div class='alert alert-success'>✅ Found " . count($cover_files) . " cover images in uploads/covers/</div>";
        
        if (count($cover_files) > 0) {
            echo "<details class='mb-3'>";
            echo "<summary class='btn btn-outline-info btn-sm'>Show available cover files</summary>";
            echo "<ul class='mt-2'>";
            foreach ($cover_files as $file) {
                echo "<li>{$file}</li>";
            }
            echo "</ul>";
            echo "</details>";
        }
    }
    
    // Check books
    $query = "SELECT COUNT(*) as total FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $total_books = $stmt->fetch()['total'];
    
    $query = "SELECT COUNT(*) as with_covers FROM books WHERE cover_image IS NOT NULL AND cover_image != ''";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $books_with_covers = $stmt->fetch()['with_covers'];
    
    $books_without_covers = $total_books - $books_with_covers;
    
    echo "<div class='row'>";
    echo "<div class='col-md-4'>";
    echo "<div class='card text-center'>";
    echo "<div class='card-body'>";
    echo "<h5 class='card-title'>Total Books</h5>";
    echo "<h2 class='text-primary'>{$total_books}</h2>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card text-center'>";
    echo "<div class='card-body'>";
    echo "<h5 class='card-title'>With Covers</h5>";
    echo "<h2 class='text-success'>{$books_with_covers}</h2>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-4'>";
    echo "<div class='card text-center'>";
    echo "<div class='card-body'>";
    echo "<h5 class='card-title'>Without Covers</h5>";
    echo "<h2 class='text-warning'>{$books_without_covers}</h2>";
    echo "</div></div></div>";
    echo "</div>";
    
    // Check for broken cover links
    $query = "SELECT id, title, cover_image FROM books WHERE cover_image IS NOT NULL AND cover_image != ''";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $books_with_covers_data = $stmt->fetchAll();
    
    $broken_covers = [];
    foreach ($books_with_covers_data as $book) {
        $cover_path = $covers_dir . basename($book['cover_image']);
        if (!file_exists($cover_path)) {
            $broken_covers[] = $book;
        }
    }
    
    if (count($broken_covers) > 0) {
        echo "<div class='alert alert-warning mt-3'>";
        echo "<h5>⚠️ Found " . count($broken_covers) . " books with broken cover links:</h5>";
        echo "<ul>";
        foreach (array_slice($broken_covers, 0, 5) as $book) {
            echo "<li>{$book['title']} - {$book['cover_image']}</li>";
        }
        if (count($broken_covers) > 5) {
            echo "<li>... and " . (count($broken_covers) - 5) . " more</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // Show sample books
    echo "<h3 class='mt-4'>Sample Books Preview</h3>";
    $query = "SELECT * FROM books ORDER BY id LIMIT 6";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $sample_books = $stmt->fetchAll();
    
    echo "<div class='row'>";
    foreach ($sample_books as $book) {
        echo "<div class='col-md-4 mb-3'>";
        echo "<div class='card'>";
        
        if (!empty($book['cover_image'])) {
            $image_path = $covers_dir . basename($book['cover_image']);
            if (file_exists($image_path)) {
                echo "<img src='{$image_path}' class='card-img-top' style='height: 200px; object-fit: cover;' alt='{$book['title']}'>";
            } else {
                echo "<div class='card-img-top d-flex align-items-center justify-content-center bg-light text-danger' style='height: 200px;'>";
                echo "<i class='bi bi-exclamation-triangle fs-1'></i>";
                echo "</div>";
            }
        } else {
            echo "<div class='card-img-top d-flex align-items-center justify-content-center bg-light' style='height: 200px;'>";
            echo "<i class='bi bi-book fs-1 text-secondary'></i>";
            echo "</div>";
        }
        
        echo "<div class='card-body'>";
        echo "<h6 class='card-title'>" . htmlspecialchars($book['title']) . "</h6>";
        echo "<p class='card-text'><small>" . htmlspecialchars($book['author']) . "</small></p>";
        echo "<p class='card-text'><small class='text-muted'>Cover: " . ($book['cover_image'] ?: 'None') . "</small></p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    if ($books_without_covers > 0 || count($broken_covers) > 0) {
        echo "<div class='mt-4'>";
        echo "<a href='fix_book_covers_display.php?action=fix' class='btn btn-warning btn-lg'>";
        echo "<i class='bi bi-tools'></i> Fix Cover Display Issues";
        echo "</a>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-success mt-4'>";
        echo "<i class='bi bi-check-circle'></i> All book covers are properly configured!";
        echo "</div>";
    }
}

echo "</div></body></html>";
?>
