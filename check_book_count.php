<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Check Book Count</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2><i class='bi bi-book-half'></i> Book Count Analysis</h2>";

$action = $_GET['action'] ?? 'check';

if ($action === 'remove_duplicates') {
    echo "<div class='alert alert-warning'><strong>Removing duplicate books...</strong></div>";
    
    // Find and remove duplicate books (same title and author)
    $duplicate_query = "
        SELECT title, author, COUNT(*) as count, GROUP_CONCAT(id) as ids
        FROM books 
        GROUP BY title, author 
        HAVING COUNT(*) > 1
    ";
    
    $stmt = $db->prepare($duplicate_query);
    $stmt->execute();
    $duplicates = $stmt->fetchAll();
    
    $removed_count = 0;
    
    foreach ($duplicates as $duplicate) {
        $ids = explode(',', $duplicate['ids']);
        // Keep the first one, remove the rest
        array_shift($ids); // Remove first ID from deletion list
        
        foreach ($ids as $id) {
            $delete_query = "DELETE FROM books WHERE id = :id";
            $delete_stmt = $db->prepare($delete_query);
            $delete_stmt->bindParam(':id', $id);
            
            if ($delete_stmt->execute()) {
                echo "<div class='alert alert-success'>✅ Removed duplicate: '{$duplicate['title']}' by {$duplicate['author']} (ID: {$id})</div>";
                $removed_count++;
            }
        }
    }
    
    if ($removed_count === 0) {
        echo "<div class='alert alert-info'>No duplicate books found to remove.</div>";
    } else {
        echo "<div class='alert alert-success'><strong>Removed {$removed_count} duplicate books!</strong></div>";
    }
    
    echo "<div class='mt-3'>";
    echo "<a href='check_book_count.php' class='btn btn-primary'>Check Count Again</a>";
    echo "</div>";
    
} else {
    // Check current book count and details
    echo "<h3>Current Book Statistics</h3>";
    
    // Total count
    $query = "SELECT COUNT(*) as total FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $total_books = $stmt->fetch()['total'];
    
    echo "<div class='alert alert-info'>";
    echo "<h4>📊 Total Books in Database: <strong>{$total_books}</strong></h4>";
    echo "</div>";
    
    // Check for duplicates
    $duplicate_query = "
        SELECT title, author, COUNT(*) as count
        FROM books 
        GROUP BY title, author 
        HAVING COUNT(*) > 1
        ORDER BY count DESC
    ";
    
    $stmt = $db->prepare($duplicate_query);
    $stmt->execute();
    $duplicates = $stmt->fetchAll();
    
    if (count($duplicates) > 0) {
        echo "<div class='alert alert-warning'>";
        echo "<h5>⚠️ Found " . count($duplicates) . " sets of duplicate books:</h5>";
        echo "<ul>";
        foreach ($duplicates as $duplicate) {
            echo "<li><strong>{$duplicate['title']}</strong> by {$duplicate['author']} - {$duplicate['count']} copies</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        echo "<div class='mb-3'>";
        echo "<a href='check_book_count.php?action=remove_duplicates' class='btn btn-warning'>";
        echo "<i class='bi bi-trash'></i> Remove Duplicate Books";
        echo "</a>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-success'>✅ No duplicate books found!</div>";
    }
    
    // Show all books with details
    echo "<h3>All Books in Database</h3>";
    $query = "SELECT id, title, author, isbn, category, quantity, available_quantity, created_at FROM books ORDER BY id";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $all_books = $stmt->fetchAll();
    
    echo "<div class='table-responsive'>";
    echo "<table class='table table-striped table-sm'>";
    echo "<thead class='table-dark'>";
    echo "<tr>";
    echo "<th>ID</th>";
    echo "<th>Title</th>";
    echo "<th>Author</th>";
    echo "<th>ISBN</th>";
    echo "<th>Category</th>";
    echo "<th>Qty</th>";
    echo "<th>Available</th>";
    echo "<th>Added</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";
    
    foreach ($all_books as $book) {
        echo "<tr>";
        echo "<td>{$book['id']}</td>";
        echo "<td>" . htmlspecialchars($book['title']) . "</td>";
        echo "<td>" . htmlspecialchars($book['author']) . "</td>";
        echo "<td>" . htmlspecialchars($book['isbn'] ?: 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($book['category'] ?: 'N/A') . "</td>";
        echo "<td>{$book['quantity']}</td>";
        echo "<td>{$book['available_quantity']}</td>";
        echo "<td>" . date('Y-m-d', strtotime($book['created_at'])) . "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>";
    echo "</table>";
    echo "</div>";
    
    // Check for books added by different scripts
    echo "<h3>Books by Creation Date</h3>";
    $date_query = "
        SELECT DATE(created_at) as date, COUNT(*) as count 
        FROM books 
        GROUP BY DATE(created_at) 
        ORDER BY date DESC
    ";
    
    $stmt = $db->prepare($date_query);
    $stmt->execute();
    $dates = $stmt->fetchAll();
    
    echo "<div class='row'>";
    foreach ($dates as $date_info) {
        echo "<div class='col-md-4 mb-2'>";
        echo "<div class='card'>";
        echo "<div class='card-body text-center'>";
        echo "<h6>{$date_info['date']}</h6>";
        echo "<h4 class='text-primary'>{$date_info['count']} books</h4>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
    
    // Check for potential issues
    echo "<h3>Potential Issues</h3>";
    
    // Books with same ISBN
    $isbn_query = "
        SELECT isbn, COUNT(*) as count, GROUP_CONCAT(title) as titles
        FROM books 
        WHERE isbn IS NOT NULL AND isbn != ''
        GROUP BY isbn 
        HAVING COUNT(*) > 1
    ";
    
    $stmt = $db->prepare($isbn_query);
    $stmt->execute();
    $isbn_duplicates = $stmt->fetchAll();
    
    if (count($isbn_duplicates) > 0) {
        echo "<div class='alert alert-danger'>";
        echo "<h5>❌ Books with duplicate ISBNs:</h5>";
        echo "<ul>";
        foreach ($isbn_duplicates as $isbn_dup) {
            echo "<li>ISBN: {$isbn_dup['isbn']} - {$isbn_dup['count']} books: {$isbn_dup['titles']}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    // Books with no ISBN
    $no_isbn_query = "SELECT COUNT(*) as count FROM books WHERE isbn IS NULL OR isbn = ''";
    $stmt = $db->prepare($no_isbn_query);
    $stmt->execute();
    $no_isbn_count = $stmt->fetch()['count'];
    
    if ($no_isbn_count > 0) {
        echo "<div class='alert alert-warning'>";
        echo "<h5>⚠️ {$no_isbn_count} books have no ISBN</h5>";
        echo "</div>";
    }
    
    // Summary
    echo "<div class='mt-4'>";
    echo "<div class='card'>";
    echo "<div class='card-header'>";
    echo "<h5>📋 Summary</h5>";
    echo "</div>";
    echo "<div class='card-body'>";
    echo "<p><strong>Expected books:</strong> 15</p>";
    echo "<p><strong>Actual books:</strong> {$total_books}</p>";
    echo "<p><strong>Difference:</strong> " . ($total_books - 15) . "</p>";
    
    if ($total_books > 15) {
        echo "<p class='text-warning'>You have " . ($total_books - 15) . " extra books in the database.</p>";
        if (count($duplicates) > 0) {
            echo "<p class='text-info'>Some of these might be duplicates that can be removed.</p>";
        }
    } elseif ($total_books < 15) {
        echo "<p class='text-danger'>You have " . (15 - $total_books) . " fewer books than expected.</p>";
    } else {
        echo "<p class='text-success'>Book count matches expected number!</p>";
    }
    echo "</div>";
    echo "</div>";
    echo "</div>";
}

echo "</div></body></html>";
?>
