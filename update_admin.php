<?php
/**
 * Update Admin User Script
 * This script updates the admin user's email and password
 */

// Database connection parameters
$host = 'localhost';
$dbname = 'lms_db';
$username = 'root';
$password = 'password1234';

// New admin credentials
$admin_username = 'admin';
$admin_email = '<EMAIL>';
$admin_password = 'admin123';

try {
    // Connect to the database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<h2>Update Admin User</h2>";

    // Hash the password
    $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);

    // Update the admin user
    $query = "UPDATE users SET email = :email, password = :password WHERE username = :username AND role = 'admin'";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':email', $admin_email);
    $stmt->bindParam(':password', $hashed_password);
    $stmt->bindParam(':username', $admin_username);

    if ($stmt->execute()) {
        echo "<p>✅ Admin user updated successfully!</p>";
        echo "<p>New admin credentials:</p>";
        echo "<ul>";
        echo "<li><strong>Username:</strong> $admin_username</li>";
        echo "<li><strong>Email:</strong> $admin_email</li>";
        echo "<li><strong>Password:</strong> $admin_password</li>";
        echo "</ul>";
        echo "<p>You can now log in with these credentials at <a href='auth/login.php'>the admin login page</a>.</p>";
    } else {
        echo "<p>❌ Failed to update admin user.</p>";
    }

} catch (PDOException $e) {
    echo "<h2>Error!</h2>";
    echo "<p>An error occurred: " . $e->getMessage() . "</p>";
}
?>
