# Library Management System - Entity Relationship Diagram (ERD)

## Database Schema Overview

This ERD represents the complete database structure for the Library Management System with all entities, attributes, and relationships.

## Entities and Relationships

### 1. **USERS** (Admin & Librarian Staff)
```
┌─────────────────────────────────────┐
│                USERS                │
├─────────────────────────────────────┤
│ PK  id (INT, AUTO_INCREMENT)        │
│     username (VA<PERSON><PERSON><PERSON>(50), UNIQUE)  │
│     password (VARCHAR(255))         │
│     email (VARCHAR(100), UNIQUE)    │
│     role (ENUM: admin, librarian)   │
│     full_name (VARCHAR(100))        │
│     remember_token (VARCHAR(255))   │
│     created_at (TIMESTAMP)          │
│     updated_at (TIMESTAMP)          │
└─────────────────────────────────────┘
```

### 2. **MEMBERS** (Library Members)
```
┌─────────────────────────────────────┐
│               MEMBERS               │
├─────────────────────────────────────┤
│ PK  id (INT, AUTO_INCREMENT)        │
│     first_name (VARCHAR(50))        │
│     last_name (VARCHAR(50))         │
│     email (VARCHAR(100), UNIQUE)    │
│     password (VARCHAR(255))         │
│     phone (VARCHAR(20))             │
│     address (TEXT)                  │
│     membership_date (DATE)          │
│     membership_status (ENUM)        │
│     remember_token (VARCHAR(255))   │
│     created_at (TIMESTAMP)          │
│     updated_at (TIMESTAMP)          │
└─────────────────────────────────────┘
```

### 3. **BOOKS** (Library Collection)
```
┌─────────────────────────────────────┐
│                BOOKS                │
├─────────────────────────────────────┤
│ PK  id (INT, AUTO_INCREMENT)        │
│     isbn (VARCHAR(20), UNIQUE)      │
│     title (VARCHAR(255))            │
│     author (VARCHAR(100))           │
│     category (VARCHAR(50))          │
│     publication_year (INT)          │
│     publisher (VARCHAR(100))        │
│     quantity (INT)                  │
│     available_quantity (INT)        │
│     shelf_location (VARCHAR(50))    │
│     description (TEXT)              │
│     cover_image (VARCHAR(255))      │
│     created_at (TIMESTAMP)          │
│     updated_at (TIMESTAMP)          │
└─────────────────────────────────────┘
```

### 4. **BOOK_LOANS** (Borrowing Records)
```
┌─────────────────────────────────────┐
│             BOOK_LOANS              │
├─────────────────────────────────────┤
│ PK  id (INT, AUTO_INCREMENT)        │
│ FK  book_id (INT) → books.id        │
│ FK  member_id (INT) → members.id    │
│     issue_date (DATE)               │
│     due_date (DATE)                 │
│     return_date (DATE)              │
│     fine (DECIMAL(10,2))            │
│     status (ENUM)                   │
│     renewal_count (INT)             │
│     renewal_date (TIMESTAMP)        │
│     created_at (TIMESTAMP)          │
│     updated_at (TIMESTAMP)          │
└─────────────────────────────────────┘
```

### 5. **BOOK_RESERVATIONS** (Book Reservations)
```
┌─────────────────────────────────────┐
│          BOOK_RESERVATIONS          │
├─────────────────────────────────────┤
│ PK  id (INT, AUTO_INCREMENT)        │
│ FK  book_id (INT) → books.id        │
│ FK  member_id (INT) → members.id    │
│     reservation_date (DATE)         │
│     expiry_date (DATE)              │
│     status (ENUM)                   │
│     created_at (TIMESTAMP)          │
│     updated_at (TIMESTAMP)          │
└─────────────────────────────────────┘
```

### 6. **FINES** (Fine Management)
```
┌─────────────────────────────────────┐
│                FINES                │
├─────────────────────────────────────┤
│ PK  id (INT, AUTO_INCREMENT)        │
│ FK  member_id (INT) → members.id    │
│ FK  loan_id (INT) → book_loans.id   │
│     amount (DECIMAL(10,2))          │
│     status (ENUM)                   │
│     payment_method (ENUM)           │
│     payment_reference (VARCHAR(100))│
│     created_date (TIMESTAMP)        │
│     paid_date (TIMESTAMP)           │
│     waived_date (TIMESTAMP)         │
│ FK  processed_by (INT) → users.id   │
│     notes (TEXT)                    │
└─────────────────────────────────────┘
```

### 7. **PAYMENT_TRANSACTIONS** (Payment Records)
```
┌─────────────────────────────────────┐
│         PAYMENT_TRANSACTIONS        │
├─────────────────────────────────────┤
│ PK  id (INT, AUTO_INCREMENT)        │
│ FK  fine_id (INT) → fines.id        │
│ FK  member_id (INT) → members.id    │
│     amount (DECIMAL(10,2))          │
│     payment_method (ENUM)           │
│     payment_reference (VARCHAR(100))│
│     transaction_date (TIMESTAMP)    │
│ FK  processed_by (INT) → users.id   │
│     receipt_number (VARCHAR(50))    │
│     status (ENUM)                   │
│     notes (TEXT)                    │
└─────────────────────────────────────┘
```

### 8. **BOOK_REVIEWS** (Member Reviews & Ratings)
```
┌─────────────────────────────────────┐
│            BOOK_REVIEWS             │
├─────────────────────────────────────┤
│ PK  id (INT, AUTO_INCREMENT)        │
│ FK  book_id (INT) → books.id        │
│ FK  member_id (INT) → members.id    │
│     rating (INT, 1-5)               │
│     review_text (TEXT)              │
│     review_date (TIMESTAMP)         │
│     is_approved (BOOLEAN)           │
│     helpful_count (INT)             │
│     created_at (TIMESTAMP)          │
│     updated_at (TIMESTAMP)          │
└─────────────────────────────────────┘
```

### 9. **MEMBER_WISHLIST** (Member Wishlist)
```
┌─────────────────────────────────────┐
│           MEMBER_WISHLIST           │
├─────────────────────────────────────┤
│ PK  id (INT, AUTO_INCREMENT)        │
│ FK  member_id (INT) → members.id    │
│ FK  book_id (INT) → books.id        │
│     added_date (TIMESTAMP)          │
│     notes (TEXT)                    │
│     priority (ENUM)                 │
│     created_at (TIMESTAMP)          │
│     updated_at (TIMESTAMP)          │
└─────────────────────────────────────┘
```

### 10. **NOTIFICATIONS** (System Notifications)
```
┌─────────────────────────────────────┐
│            NOTIFICATIONS            │
├─────────────────────────────────────┤
│ PK  id (INT, AUTO_INCREMENT)        │
│ FK  user_id (INT) → users.id        │
│     message (TEXT)                  │
│     type (ENUM)                     │
│     entity_type (VARCHAR(50))       │
│     entity_id (INT)                 │
│     is_read (BOOLEAN)               │
│     created_at (TIMESTAMP)          │
└─────────────────────────────────────┘
```

### 11. **ACTIVITY_LOG** (System Activity Tracking)
```
┌─────────────────────────────────────┐
│            ACTIVITY_LOG             │
├─────────────────────────────────────┤
│ PK  id (INT, AUTO_INCREMENT)        │
│ FK  user_id (INT) → users.id        │
│     action (VARCHAR(100))           │
│     description (TEXT)              │
│     entity_type (VARCHAR(50))       │
│     entity_id (INT)                 │
│     ip_address (VARCHAR(45))        │
│     user_agent (TEXT)               │
│     timestamp (TIMESTAMP)           │
└─────────────────────────────────────┘
```

## Relationship Summary

### One-to-Many Relationships:
1. **MEMBERS** → **BOOK_LOANS** (1:M)
2. **BOOKS** → **BOOK_LOANS** (1:M)
3. **MEMBERS** → **BOOK_RESERVATIONS** (1:M)
4. **BOOKS** → **BOOK_RESERVATIONS** (1:M)
5. **MEMBERS** → **FINES** (1:M)
6. **BOOK_LOANS** → **FINES** (1:M)
7. **FINES** → **PAYMENT_TRANSACTIONS** (1:M)
8. **MEMBERS** → **PAYMENT_TRANSACTIONS** (1:M)
9. **BOOKS** → **BOOK_REVIEWS** (1:M)
10. **MEMBERS** → **BOOK_REVIEWS** (1:M)
11. **MEMBERS** → **MEMBER_WISHLIST** (1:M)
12. **BOOKS** → **MEMBER_WISHLIST** (1:M)
13. **USERS** → **NOTIFICATIONS** (1:M)
14. **USERS** → **ACTIVITY_LOG** (1:M)
15. **USERS** → **FINES** (processed_by) (1:M)
16. **USERS** → **PAYMENT_TRANSACTIONS** (processed_by) (1:M)

### Many-to-Many Relationships (through junction tables):
1. **MEMBERS** ↔ **BOOKS** (through BOOK_LOANS)
2. **MEMBERS** ↔ **BOOKS** (through BOOK_RESERVATIONS)
3. **MEMBERS** ↔ **BOOKS** (through BOOK_REVIEWS)
4. **MEMBERS** ↔ **BOOKS** (through MEMBER_WISHLIST)

## Key Features:
- **User Management**: Separate admin/librarian users and library members
- **Book Management**: Complete book catalog with inventory tracking
- **Circulation**: Book borrowing, returns, renewals, and reservations
- **Financial**: Fine calculation, payment processing, and transaction tracking
- **Member Features**: Wishlist, reviews, ratings system
- **System Features**: Notifications, activity logging, and audit trails
- **Payment Methods**: Multiple payment options (cash, digital payments)
