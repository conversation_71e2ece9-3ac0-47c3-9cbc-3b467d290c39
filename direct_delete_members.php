<?php
/**
 * Direct Delete Members - Super Simple Approach
 * This will directly delete members using the simplest method possible
 */

// Enable all error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🗑️ Direct Delete Members</h1>";
echo "<p>This will directly delete excess members to keep only 1000.</p>";

try {
    // Database connection
    $host = 'localhost';
    $dbname = 'lms_db'; // Change this if your database name is different
    $username = 'root';
    $password = 'password1234';

    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<p>✅ Database connected successfully</p>";

    // Check current member count
    $count_sql = "SELECT COUNT(*) as total FROM members";
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute();
    $current_total = $count_stmt->fetch()['total'];

    echo "<p><strong>Current Members:</strong> {$current_total}</p>";

    if ($current_total <= 1000) {
        echo "<p style='color: green;'>✅ Already have 1000 or fewer members!</p>";
        exit;
    }

    $to_delete = $current_total - 1000;
    echo "<p><strong>Need to delete:</strong> {$to_delete} members</p>";

    // Method 1: Delete by ID range
    echo "<h3>Method 1: Delete by ID range</h3>";

    // Get the 1000th member ID
    $limit_sql = "SELECT id FROM members ORDER BY id LIMIT 999, 1";
    $limit_stmt = $pdo->prepare($limit_sql);
    $limit_stmt->execute();
    $limit_result = $limit_stmt->fetch();

    if ($limit_result) {
        $max_id_to_keep = $limit_result['id'];
        echo "<p>Keeping members with ID <= {$max_id_to_keep}</p>";

        // Delete loans first
        $delete_loans_sql = "DELETE FROM book_loans WHERE member_id > {$max_id_to_keep}";
        $delete_loans_stmt = $pdo->prepare($delete_loans_sql);
        $delete_loans_stmt->execute();
        $deleted_loans = $delete_loans_stmt->rowCount();
        echo "<p>🗑️ Deleted {$deleted_loans} loans</p>";

        // Delete members
        $delete_members_sql = "DELETE FROM members WHERE id > {$max_id_to_keep}";
        $delete_members_stmt = $pdo->prepare($delete_members_sql);
        $delete_members_stmt->execute();
        $deleted_members = $delete_members_stmt->rowCount();
        echo "<p>🗑️ Deleted {$deleted_members} members</p>";

    } else {
        echo "<p style='color: red;'>❌ Could not find 1000th member</p>";

        // Method 2: Delete in batches
        echo "<h3>Method 2: Delete in batches</h3>";

        $batch_size = 500;
        $deleted_total = 0;

        while ($deleted_total < $to_delete) {
            // Delete loans first
            $delete_loans_batch = "DELETE FROM book_loans
                                  WHERE member_id IN (
                                      SELECT id FROM (
                                          SELECT id FROM members ORDER BY id DESC LIMIT {$batch_size}
                                      ) AS temp
                                  )";
            $pdo->exec($delete_loans_batch);

            // Delete members
            $delete_members_batch = "DELETE FROM members ORDER BY id DESC LIMIT {$batch_size}";
            $delete_stmt = $pdo->prepare($delete_members_batch);
            $delete_stmt->execute();
            $batch_deleted = $delete_stmt->rowCount();

            $deleted_total += $batch_deleted;
            echo "<p>Batch deleted: {$batch_deleted} members (Total: {$deleted_total})</p>";

            if ($batch_deleted == 0) break; // No more to delete
        }
    }

    // Check final count
    $final_count_stmt = $pdo->prepare($count_sql);
    $final_count_stmt->execute();
    $final_total = $final_count_stmt->fetch()['total'];

    echo "<hr>";
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 5px;'>";
    echo "<h3>✅ Deletion Complete!</h3>";
    echo "<p><strong>Before:</strong> {$current_total} members</p>";
    echo "<p><strong>After:</strong> {$final_total} members</p>";
    echo "<p><strong>Deleted:</strong> " . ($current_total - $final_total) . " members</p>";
    echo "</div>";

    // Check loan counts
    $active_loans = $pdo->query("SELECT COUNT(*) as count FROM book_loans WHERE status = 'borrowed'")->fetch()['count'];
    $total_loans = $pdo->query("SELECT COUNT(*) as count FROM book_loans")->fetch()['count'];

    echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>📊 Current Loan Stats</h3>";
    echo "<p><strong>Active Loans:</strong> {$active_loans}</p>";
    echo "<p><strong>Total Loans:</strong> {$total_loans}</p>";
    echo "</div>";

    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎉 Success!</h3>";
    echo "<p>Members have been reduced. Please refresh your admin dashboard to see the changes.</p>";
    echo "<p><a href='admin/dashboard.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Refresh Dashboard</a></p>";
    echo "</div>";

} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Check if:</strong></p>";
    echo "<ul>";
    echo "<li>Database name is correct (currently set to 'lms_db')</li>";
    echo "<li>MySQL is running</li>";
    echo "<li>Database credentials are correct</li>";
    echo "</ul>";
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ General Error</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

p {
    margin: 10px 0;
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 2px solid #dee2e6;
}
</style>
