<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Remove 5 Books</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2><i class='bi bi-dash-circle'></i> Remove 5 Books (20 → 15)</h2>";

$action = $_GET['action'] ?? 'check';

if ($action === 'remove_5') {
    echo "<div class='alert alert-warning'><strong>Removing 5 books to get exactly 15...</strong></div>";
    
    // Get all books, prioritize removing those without cover images first
    $query = "SELECT id, title, author, cover_image FROM books ORDER BY id";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $all_books = $stmt->fetchAll();
    
    $books_without_covers = [];
    $books_with_covers = [];
    
    // Separate books with and without cover images
    foreach ($all_books as $book) {
        $has_valid_cover = false;
        
        if (!empty($book['cover_image'])) {
            $cover_paths = [
                'uploads/covers/' . $book['cover_image'],
                'uploads/covers/' . basename($book['cover_image'])
            ];
            
            foreach ($cover_paths as $path) {
                if (file_exists($path)) {
                    $has_valid_cover = true;
                    break;
                }
            }
        }
        
        if ($has_valid_cover) {
            $books_with_covers[] = $book;
        } else {
            $books_without_covers[] = $book;
        }
    }
    
    echo "<div class='alert alert-info'>";
    echo "<h5>📊 Current Status:</h5>";
    echo "<p>Total books: <strong>" . count($all_books) . "</strong></p>";
    echo "<p>Books with covers: <strong>" . count($books_with_covers) . "</strong></p>";
    echo "<p>Books without covers: <strong>" . count($books_without_covers) . "</strong></p>";
    echo "</div>";
    
    // Select 5 books to remove (prioritize those without covers)
    $books_to_remove = [];
    
    // First, add books without covers (up to 5)
    $books_to_remove = array_slice($books_without_covers, 0, 5);
    
    // If we need more books to remove, add some with covers
    if (count($books_to_remove) < 5) {
        $remaining_to_remove = 5 - count($books_to_remove);
        $additional_books = array_slice($books_with_covers, -$remaining_to_remove); // Take from the end
        $books_to_remove = array_merge($books_to_remove, $additional_books);
    }
    
    echo "<div class='alert alert-warning'>";
    echo "<h5>📋 Removal Plan:</h5>";
    echo "<p>Books to remove: <strong>" . count($books_to_remove) . "</strong></p>";
    echo "<p>Books remaining: <strong>" . (count($all_books) - count($books_to_remove)) . "</strong></p>";
    echo "</div>";
    
    if (count($books_to_remove) > 0) {
        echo "<h4>Books that will be removed (" . count($books_to_remove) . "):</h4>";
        echo "<div class='row mb-3'>";
        foreach ($books_to_remove as $book) {
            echo "<div class='col-md-4 mb-2'>";
            echo "<div class='card border-danger'>";
            
            // Show cover image if available
            $cover_path = '';
            if (!empty($book['cover_image'])) {
                $possible_paths = [
                    'uploads/covers/' . $book['cover_image'],
                    'uploads/covers/' . basename($book['cover_image'])
                ];
                
                foreach ($possible_paths as $path) {
                    if (file_exists($path)) {
                        $cover_path = $path;
                        break;
                    }
                }
            }
            
            if ($cover_path) {
                echo "<img src='{$cover_path}' class='card-img-top' style='height: 150px; object-fit: cover;' alt='{$book['title']}'>";
            } else {
                echo "<div class='card-img-top d-flex align-items-center justify-content-center bg-light' style='height: 150px;'>";
                echo "<i class='bi bi-book fs-2 text-secondary'></i>";
                echo "</div>";
            }
            
            echo "<div class='card-body p-2'>";
            echo "<h6 class='card-title text-danger'>" . htmlspecialchars($book['title']) . "</h6>";
            echo "<p class='card-text'><small>by " . htmlspecialchars($book['author']) . "</small></p>";
            echo "<p class='card-text'><small class='text-muted'>" . ($cover_path ? 'Has cover' : 'No cover') . "</small></p>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        echo "</div>";
        
        // Remove the selected books
        $removed_count = 0;
        foreach ($books_to_remove as $book) {
            // Check for active loans
            $loan_check = "SELECT COUNT(*) as count FROM book_loans WHERE book_id = :book_id AND status IN ('borrowed', 'overdue')";
            $loan_stmt = $db->prepare($loan_check);
            $loan_stmt->bindParam(':book_id', $book['id']);
            $loan_stmt->execute();
            $active_loans = $loan_stmt->fetch()['count'];
            
            if ($active_loans > 0) {
                echo "<div class='alert alert-warning'>⚠️ Skipping '{$book['title']}' - has {$active_loans} active loan(s)</div>";
                continue;
            }
            
            // Remove related data first
            $delete_reservations = "DELETE FROM book_reservations WHERE book_id = :book_id";
            $res_stmt = $db->prepare($delete_reservations);
            $res_stmt->bindParam(':book_id', $book['id']);
            $res_stmt->execute();
            
            $delete_loans = "DELETE FROM book_loans WHERE book_id = :book_id";
            $loan_stmt = $db->prepare($delete_loans);
            $loan_stmt->bindParam(':book_id', $book['id']);
            $loan_stmt->execute();
            
            // Remove the book
            $delete_book = "DELETE FROM books WHERE id = :book_id";
            $book_stmt = $db->prepare($delete_book);
            $book_stmt->bindParam(':book_id', $book['id']);
            
            if ($book_stmt->execute()) {
                echo "<div class='alert alert-success'>✅ Removed: '{$book['title']}' by {$book['author']}</div>";
                $removed_count++;
            } else {
                echo "<div class='alert alert-danger'>❌ Failed to remove: '{$book['title']}'</div>";
            }
        }
        
        echo "<div class='alert alert-success'><strong>✅ Successfully removed {$removed_count} books!</strong></div>";
        
        // Show final count
        $final_query = "SELECT COUNT(*) as total FROM books";
        $final_stmt = $db->prepare($final_query);
        $final_stmt->execute();
        $final_count = $final_stmt->fetch()['total'];
        
        echo "<div class='alert alert-info'><h4>🎯 Final Result: {$final_count} books remaining in the database</h4></div>";
        
        if ($final_count == 15) {
            echo "<div class='alert alert-success'><h4>🎉 Perfect! You now have exactly 15 books!</h4></div>";
        }
        
    } else {
        echo "<div class='alert alert-danger'>No books found to remove!</div>";
    }
    
    echo "<div class='mt-3'>";
    echo "<a href='catalog.php' class='btn btn-primary btn-lg me-2'>View Catalog</a>";
    echo "<a href='remove_5_books.php' class='btn btn-secondary'>Check Again</a>";
    echo "</div>";
    
} else {
    // Check current status
    echo "<h3>Current Status</h3>";
    
    $query = "SELECT COUNT(*) as total FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $total_books = $stmt->fetch()['total'];
    
    echo "<div class='alert alert-info text-center'>";
    echo "<h4>📚 Current Total: <strong>{$total_books} books</strong></h4>";
    echo "<h5>🎯 Target: <strong>15 books</strong></h5>";
    echo "<h5>➖ Need to remove: <strong>" . ($total_books - 15) . " books</strong></h5>";
    echo "</div>";
    
    if ($total_books > 15) {
        // Show which books will be removed (preview)
        $query = "SELECT id, title, author, cover_image FROM books ORDER BY id";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $all_books = $stmt->fetchAll();
        
        $books_without_covers = [];
        $books_with_covers = [];
        
        foreach ($all_books as $book) {
            $has_valid_cover = false;
            
            if (!empty($book['cover_image'])) {
                $cover_paths = [
                    'uploads/covers/' . $book['cover_image'],
                    'uploads/covers/' . basename($book['cover_image'])
                ];
                
                foreach ($cover_paths as $path) {
                    if (file_exists($path)) {
                        $has_valid_cover = true;
                        break;
                    }
                }
            }
            
            if ($has_valid_cover) {
                $books_with_covers[] = $book;
            } else {
                $books_without_covers[] = $book;
            }
        }
        
        echo "<div class='row mb-4'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card text-center'>";
        echo "<div class='card-body'>";
        echo "<h5 class='card-title text-success'>Books WITH Pictures</h5>";
        echo "<h2 class='text-success'>" . count($books_with_covers) . "</h2>";
        echo "<p class='text-muted'>Will be kept</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card text-center'>";
        echo "<div class='card-body'>";
        echo "<h5 class='card-title text-warning'>Books WITHOUT Pictures</h5>";
        echo "<h2 class='text-warning'>" . count($books_without_covers) . "</h2>";
        echo "<p class='text-muted'>Will be removed first</p>";
        echo "</div></div></div>";
        echo "</div>";
        
        // Preview which books will be removed
        $books_to_remove_preview = [];
        $books_to_remove_preview = array_slice($books_without_covers, 0, 5);
        
        if (count($books_to_remove_preview) < 5) {
            $remaining_to_remove = 5 - count($books_to_remove_preview);
            $additional_books = array_slice($books_with_covers, -$remaining_to_remove);
            $books_to_remove_preview = array_merge($books_to_remove_preview, $additional_books);
        }
        
        echo "<div class='alert alert-warning'>";
        echo "<h5>📋 Removal Strategy:</h5>";
        echo "<p>• <strong>Priority 1:</strong> Remove books without cover images first</p>";
        echo "<p>• <strong>Priority 2:</strong> If needed, remove some books with covers</p>";
        echo "<p>• <strong>Goal:</strong> Remove exactly 5 books to reach 15 total</p>";
        echo "</div>";
        
        if (count($books_to_remove_preview) > 0) {
            echo "<h4>Preview: Books that will be removed (" . count($books_to_remove_preview) . "):</h4>";
            echo "<div class='table-responsive mb-3'>";
            echo "<table class='table table-striped'>";
            echo "<thead class='table-danger'>";
            echo "<tr><th>Title</th><th>Author</th><th>Has Cover</th></tr>";
            echo "</thead><tbody>";
            
            foreach ($books_to_remove_preview as $book) {
                $has_cover = !empty($book['cover_image']) && 
                            (file_exists('uploads/covers/' . $book['cover_image']) || 
                             file_exists('uploads/covers/' . basename($book['cover_image'])));
                
                echo "<tr>";
                echo "<td>" . htmlspecialchars($book['title']) . "</td>";
                echo "<td>" . htmlspecialchars($book['author']) . "</td>";
                echo "<td>" . ($has_cover ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>') . "</td>";
                echo "</tr>";
            }
            echo "</tbody></table>";
            echo "</div>";
        }
        
        echo "<div class='mt-3'>";
        echo "<a href='remove_5_books.php?action=remove_5' class='btn btn-danger btn-lg'>";
        echo "<i class='bi bi-dash-circle'></i> Remove 5 Books (20 → 15)";
        echo "</a>";
        echo "</div>";
        
    } elseif ($total_books == 15) {
        echo "<div class='alert alert-success'>";
        echo "<h4>🎉 Perfect! You already have exactly 15 books!</h4>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-info'>";
        echo "<h4>You have fewer than 15 books. No need to remove any.</h4>";
        echo "</div>";
    }
}

echo "</div></body></html>";
?>
