<?php
/**
 * Database Connection Test Script
 * This script tests the database connection with the updated password
 */

echo "<h1>Database Connection Test</h1>";
echo "<p>Testing database connection with password 'password1234'...</p>";

// Test 1: Direct PDO connection
echo "<h2>Test 1: Direct PDO Connection</h2>";
try {
    $host = 'localhost';
    $dbname = 'lms_db';
    $username = 'root';
    $password = 'password1234';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Direct PDO connection successful!</p>";
    
    // Test a simple query
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    echo "<p style='color: green;'>✅ Test query successful!</p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Direct PDO connection failed: " . $e->getMessage() . "</p>";
}

// Test 2: Using Database class
echo "<h2>Test 2: Database Class Connection</h2>";
try {
    require_once 'config/database.php';
    
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<p style='color: green;'>✅ Database class connection successful!</p>";
        
        // Test connection method
        $test_result = $database->testConnection();
        if ($test_result['success']) {
            echo "<p style='color: green;'>✅ Database test method successful: " . $test_result['message'] . "</p>";
        } else {
            echo "<p style='color: red;'>❌ Database test method failed: " . $test_result['message'] . "</p>";
        }
        
        // Test basic queries
        echo "<h3>Testing Basic Queries</h3>";
        
        $tables = ['users', 'books', 'members', 'book_loans'];
        foreach ($tables as $table) {
            try {
                $stmt = $db->prepare("SELECT COUNT(*) as count FROM $table");
                $stmt->execute();
                $result = $stmt->fetch();
                echo "<p style='color: green;'>✅ Table '$table': " . $result['count'] . " records</p>";
            } catch (Exception $e) {
                echo "<p style='color: orange;'>⚠️ Table '$table': " . $e->getMessage() . "</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ Database class connection failed!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database class error: " . $e->getMessage() . "</p>";
}

// Test 3: Check MySQL server connection without database
echo "<h2>Test 3: MySQL Server Connection (without database)</h2>";
try {
    $pdo_server = new PDO("mysql:host=localhost", "root", "password1234");
    $pdo_server->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ MySQL server connection successful!</p>";
    
    // Check if database exists
    $stmt = $pdo_server->query("SHOW DATABASES LIKE 'lms_db'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ Database 'lms_db' exists!</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Database 'lms_db' does not exist!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ MySQL server connection failed: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>Summary</h2>";
echo "<p>If all tests above show green checkmarks (✅), your database connection is working correctly with the password 'password1234'.</p>";
echo "<p>If you see any red X marks (❌), there may still be connection issues.</p>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ul>";
echo "<li>If tests are successful, you can now use your LMS system normally</li>";
echo "<li>If tests fail, check that MySQL is running and the password is correct</li>";
echo "<li>You can access the admin dashboard at <a href='admin/dashboard.php'>admin/dashboard.php</a></li>";
echo "</ul>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f8f9fa;
}

h1, h2, h3 {
    color: #333;
}

p {
    margin: 10px 0;
}

hr {
    margin: 30px 0;
    border: none;
    border-top: 2px solid #dee2e6;
}

ul {
    background: #e9ecef;
    padding: 15px;
    border-radius: 5px;
}
</style>
