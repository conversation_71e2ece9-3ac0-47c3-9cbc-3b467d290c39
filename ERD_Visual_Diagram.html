<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Library Management System - ERD</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .erd-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .entity {
            border: 2px solid #333;
            border-radius: 8px;
            background: #fff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .entity-header {
            background: #2c3e50;
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            font-size: 14px;
        }
        .entity-body {
            padding: 10px;
        }
        .attribute {
            padding: 3px 0;
            font-size: 12px;
            border-bottom: 1px solid #eee;
        }
        .pk {
            color: #e74c3c;
            font-weight: bold;
        }
        .fk {
            color: #3498db;
            font-weight: bold;
        }
        .relationships {
            margin-top: 30px;
        }
        .relationship-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #ecf0f1;
            border-radius: 5px;
        }
        .relationship-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        .relationship-list {
            list-style-type: none;
            padding: 0;
        }
        .relationship-list li {
            padding: 5px 0;
            border-bottom: 1px solid #bdc3c7;
        }
        .one-to-many {
            color: #27ae60;
        }
        .many-to-many {
            color: #8e44ad;
        }
        .legend {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 20px;
            padding: 15px;
            background: #34495e;
            color: white;
            border-radius: 5px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Library Management System - Entity Relationship Diagram</h1>
        
        <div class="legend">
            <div class="legend-item">
                <span style="color: #e74c3c;">●</span> Primary Key (PK)
            </div>
            <div class="legend-item">
                <span style="color: #3498db;">●</span> Foreign Key (FK)
            </div>
            <div class="legend-item">
                <span style="color: #27ae60;">●</span> One-to-Many
            </div>
            <div class="legend-item">
                <span style="color: #8e44ad;">●</span> Many-to-Many
            </div>
        </div>

        <div class="erd-container">
            <!-- USERS Entity -->
            <div class="entity">
                <div class="entity-header">USERS</div>
                <div class="entity-body">
                    <div class="attribute pk">PK id (INT)</div>
                    <div class="attribute">username (VARCHAR(50))</div>
                    <div class="attribute">password (VARCHAR(255))</div>
                    <div class="attribute">email (VARCHAR(100))</div>
                    <div class="attribute">role (ENUM)</div>
                    <div class="attribute">full_name (VARCHAR(100))</div>
                    <div class="attribute">remember_token (VARCHAR(255))</div>
                    <div class="attribute">created_at (TIMESTAMP)</div>
                    <div class="attribute">updated_at (TIMESTAMP)</div>
                </div>
            </div>

            <!-- MEMBERS Entity -->
            <div class="entity">
                <div class="entity-header">MEMBERS</div>
                <div class="entity-body">
                    <div class="attribute pk">PK id (INT)</div>
                    <div class="attribute">first_name (VARCHAR(50))</div>
                    <div class="attribute">last_name (VARCHAR(50))</div>
                    <div class="attribute">email (VARCHAR(100))</div>
                    <div class="attribute">password (VARCHAR(255))</div>
                    <div class="attribute">phone (VARCHAR(20))</div>
                    <div class="attribute">address (TEXT)</div>
                    <div class="attribute">membership_date (DATE)</div>
                    <div class="attribute">membership_status (ENUM)</div>
                    <div class="attribute">remember_token (VARCHAR(255))</div>
                    <div class="attribute">created_at (TIMESTAMP)</div>
                    <div class="attribute">updated_at (TIMESTAMP)</div>
                </div>
            </div>

            <!-- BOOKS Entity -->
            <div class="entity">
                <div class="entity-header">BOOKS</div>
                <div class="entity-body">
                    <div class="attribute pk">PK id (INT)</div>
                    <div class="attribute">isbn (VARCHAR(20))</div>
                    <div class="attribute">title (VARCHAR(255))</div>
                    <div class="attribute">author (VARCHAR(100))</div>
                    <div class="attribute">category (VARCHAR(50))</div>
                    <div class="attribute">publication_year (INT)</div>
                    <div class="attribute">publisher (VARCHAR(100))</div>
                    <div class="attribute">quantity (INT)</div>
                    <div class="attribute">available_quantity (INT)</div>
                    <div class="attribute">shelf_location (VARCHAR(50))</div>
                    <div class="attribute">description (TEXT)</div>
                    <div class="attribute">cover_image (VARCHAR(255))</div>
                    <div class="attribute">created_at (TIMESTAMP)</div>
                    <div class="attribute">updated_at (TIMESTAMP)</div>
                </div>
            </div>

            <!-- BOOK_LOANS Entity -->
            <div class="entity">
                <div class="entity-header">BOOK_LOANS</div>
                <div class="entity-body">
                    <div class="attribute pk">PK id (INT)</div>
                    <div class="attribute fk">FK book_id → books.id</div>
                    <div class="attribute fk">FK member_id → members.id</div>
                    <div class="attribute">issue_date (DATE)</div>
                    <div class="attribute">due_date (DATE)</div>
                    <div class="attribute">return_date (DATE)</div>
                    <div class="attribute">fine (DECIMAL(10,2))</div>
                    <div class="attribute">status (ENUM)</div>
                    <div class="attribute">renewal_count (INT)</div>
                    <div class="attribute">renewal_date (TIMESTAMP)</div>
                    <div class="attribute">created_at (TIMESTAMP)</div>
                    <div class="attribute">updated_at (TIMESTAMP)</div>
                </div>
            </div>

            <!-- BOOK_RESERVATIONS Entity -->
            <div class="entity">
                <div class="entity-header">BOOK_RESERVATIONS</div>
                <div class="entity-body">
                    <div class="attribute pk">PK id (INT)</div>
                    <div class="attribute fk">FK book_id → books.id</div>
                    <div class="attribute fk">FK member_id → members.id</div>
                    <div class="attribute">reservation_date (DATE)</div>
                    <div class="attribute">expiry_date (DATE)</div>
                    <div class="attribute">status (ENUM)</div>
                    <div class="attribute">created_at (TIMESTAMP)</div>
                    <div class="attribute">updated_at (TIMESTAMP)</div>
                </div>
            </div>

            <!-- FINES Entity -->
            <div class="entity">
                <div class="entity-header">FINES</div>
                <div class="entity-body">
                    <div class="attribute pk">PK id (INT)</div>
                    <div class="attribute fk">FK member_id → members.id</div>
                    <div class="attribute fk">FK loan_id → book_loans.id</div>
                    <div class="attribute">amount (DECIMAL(10,2))</div>
                    <div class="attribute">status (ENUM)</div>
                    <div class="attribute">payment_method (ENUM)</div>
                    <div class="attribute">payment_reference (VARCHAR(100))</div>
                    <div class="attribute">created_date (TIMESTAMP)</div>
                    <div class="attribute">paid_date (TIMESTAMP)</div>
                    <div class="attribute">waived_date (TIMESTAMP)</div>
                    <div class="attribute fk">FK processed_by → users.id</div>
                    <div class="attribute">notes (TEXT)</div>
                </div>
            </div>

            <!-- PAYMENT_TRANSACTIONS Entity -->
            <div class="entity">
                <div class="entity-header">PAYMENT_TRANSACTIONS</div>
                <div class="entity-body">
                    <div class="attribute pk">PK id (INT)</div>
                    <div class="attribute fk">FK fine_id → fines.id</div>
                    <div class="attribute fk">FK member_id → members.id</div>
                    <div class="attribute">amount (DECIMAL(10,2))</div>
                    <div class="attribute">payment_method (ENUM)</div>
                    <div class="attribute">payment_reference (VARCHAR(100))</div>
                    <div class="attribute">transaction_date (TIMESTAMP)</div>
                    <div class="attribute fk">FK processed_by → users.id</div>
                    <div class="attribute">receipt_number (VARCHAR(50))</div>
                    <div class="attribute">status (ENUM)</div>
                    <div class="attribute">notes (TEXT)</div>
                </div>
            </div>

            <!-- BOOK_REVIEWS Entity -->
            <div class="entity">
                <div class="entity-header">BOOK_REVIEWS</div>
                <div class="entity-body">
                    <div class="attribute pk">PK id (INT)</div>
                    <div class="attribute fk">FK book_id → books.id</div>
                    <div class="attribute fk">FK member_id → members.id</div>
                    <div class="attribute">rating (INT, 1-5)</div>
                    <div class="attribute">review_text (TEXT)</div>
                    <div class="attribute">review_date (TIMESTAMP)</div>
                    <div class="attribute">is_approved (BOOLEAN)</div>
                    <div class="attribute">helpful_count (INT)</div>
                    <div class="attribute">created_at (TIMESTAMP)</div>
                    <div class="attribute">updated_at (TIMESTAMP)</div>
                </div>
            </div>

            <!-- MEMBER_WISHLIST Entity -->
            <div class="entity">
                <div class="entity-header">MEMBER_WISHLIST</div>
                <div class="entity-body">
                    <div class="attribute pk">PK id (INT)</div>
                    <div class="attribute fk">FK member_id → members.id</div>
                    <div class="attribute fk">FK book_id → books.id</div>
                    <div class="attribute">added_date (TIMESTAMP)</div>
                    <div class="attribute">notes (TEXT)</div>
                    <div class="attribute">priority (ENUM)</div>
                    <div class="attribute">created_at (TIMESTAMP)</div>
                    <div class="attribute">updated_at (TIMESTAMP)</div>
                </div>
            </div>

            <!-- NOTIFICATIONS Entity -->
            <div class="entity">
                <div class="entity-header">NOTIFICATIONS</div>
                <div class="entity-body">
                    <div class="attribute pk">PK id (INT)</div>
                    <div class="attribute fk">FK user_id → users.id</div>
                    <div class="attribute">message (TEXT)</div>
                    <div class="attribute">type (ENUM)</div>
                    <div class="attribute">entity_type (VARCHAR(50))</div>
                    <div class="attribute">entity_id (INT)</div>
                    <div class="attribute">is_read (BOOLEAN)</div>
                    <div class="attribute">created_at (TIMESTAMP)</div>
                </div>
            </div>

            <!-- ACTIVITY_LOG Entity -->
            <div class="entity">
                <div class="entity-header">ACTIVITY_LOG</div>
                <div class="entity-body">
                    <div class="attribute pk">PK id (INT)</div>
                    <div class="attribute fk">FK user_id → users.id</div>
                    <div class="attribute">action (VARCHAR(100))</div>
                    <div class="attribute">description (TEXT)</div>
                    <div class="attribute">entity_type (VARCHAR(50))</div>
                    <div class="attribute">entity_id (INT)</div>
                    <div class="attribute">ip_address (VARCHAR(45))</div>
                    <div class="attribute">user_agent (TEXT)</div>
                    <div class="attribute">timestamp (TIMESTAMP)</div>
                </div>
            </div>
        </div>

        <div class="relationships">
            <div class="relationship-section">
                <h3>One-to-Many Relationships</h3>
                <ul class="relationship-list">
                    <li class="one-to-many">MEMBERS (1) → BOOK_LOANS (M)</li>
                    <li class="one-to-many">BOOKS (1) → BOOK_LOANS (M)</li>
                    <li class="one-to-many">MEMBERS (1) → BOOK_RESERVATIONS (M)</li>
                    <li class="one-to-many">BOOKS (1) → BOOK_RESERVATIONS (M)</li>
                    <li class="one-to-many">MEMBERS (1) → FINES (M)</li>
                    <li class="one-to-many">BOOK_LOANS (1) → FINES (M)</li>
                    <li class="one-to-many">FINES (1) → PAYMENT_TRANSACTIONS (M)</li>
                    <li class="one-to-many">MEMBERS (1) → PAYMENT_TRANSACTIONS (M)</li>
                    <li class="one-to-many">BOOKS (1) → BOOK_REVIEWS (M)</li>
                    <li class="one-to-many">MEMBERS (1) → BOOK_REVIEWS (M)</li>
                    <li class="one-to-many">MEMBERS (1) → MEMBER_WISHLIST (M)</li>
                    <li class="one-to-many">BOOKS (1) → MEMBER_WISHLIST (M)</li>
                    <li class="one-to-many">USERS (1) → NOTIFICATIONS (M)</li>
                    <li class="one-to-many">USERS (1) → ACTIVITY_LOG (M)</li>
                    <li class="one-to-many">USERS (1) → FINES (M) [processed_by]</li>
                    <li class="one-to-many">USERS (1) → PAYMENT_TRANSACTIONS (M) [processed_by]</li>
                </ul>
            </div>

            <div class="relationship-section">
                <h3>Many-to-Many Relationships (through junction tables)</h3>
                <ul class="relationship-list">
                    <li class="many-to-many">MEMBERS ↔ BOOKS (through BOOK_LOANS)</li>
                    <li class="many-to-many">MEMBERS ↔ BOOKS (through BOOK_RESERVATIONS)</li>
                    <li class="many-to-many">MEMBERS ↔ BOOKS (through BOOK_REVIEWS)</li>
                    <li class="many-to-many">MEMBERS ↔ BOOKS (through MEMBER_WISHLIST)</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
