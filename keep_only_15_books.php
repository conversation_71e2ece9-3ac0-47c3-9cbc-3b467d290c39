<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Keep Only 15 Books</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2><i class='bi bi-book-half'></i> Keep Only 15 Books</h2>";

$action = $_GET['action'] ?? 'check';

if ($action === 'reduce_to_15') {
    echo "<div class='alert alert-warning'><strong>Reducing to exactly 15 books...</strong></div>";
    
    // Get all books and prioritize those with cover images
    $query = "SELECT id, title, author, cover_image, created_at FROM books ORDER BY id";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $all_books = $stmt->fetchAll();
    
    $books_with_covers = [];
    $books_without_covers = [];
    
    // Separate books with and without valid cover images
    foreach ($all_books as $book) {
        $has_valid_cover = false;
        
        if (!empty($book['cover_image'])) {
            $cover_paths = [
                'uploads/covers/' . $book['cover_image'],
                'uploads/covers/' . basename($book['cover_image'])
            ];
            
            foreach ($cover_paths as $path) {
                if (file_exists($path)) {
                    $has_valid_cover = true;
                    break;
                }
            }
        }
        
        if ($has_valid_cover) {
            $books_with_covers[] = $book;
        } else {
            $books_without_covers[] = $book;
        }
    }
    
    echo "<div class='alert alert-info'>";
    echo "<h5>📊 Current Status:</h5>";
    echo "<p>Total books: <strong>" . count($all_books) . "</strong></p>";
    echo "<p>Books with covers: <strong>" . count($books_with_covers) . "</strong></p>";
    echo "<p>Books without covers: <strong>" . count($books_without_covers) . "</strong></p>";
    echo "</div>";
    
    // Select 15 books to keep (prioritize those with covers)
    $books_to_keep = [];
    
    // First, add books with covers (up to 15)
    $books_to_keep = array_slice($books_with_covers, 0, 15);
    
    // If we have less than 15 books with covers, add some without covers
    if (count($books_to_keep) < 15) {
        $remaining_slots = 15 - count($books_to_keep);
        $additional_books = array_slice($books_without_covers, 0, $remaining_slots);
        $books_to_keep = array_merge($books_to_keep, $additional_books);
    }
    
    // Get IDs of books to keep
    $keep_ids = array_column($books_to_keep, 'id');
    
    // Find books to remove
    $books_to_remove = [];
    foreach ($all_books as $book) {
        if (!in_array($book['id'], $keep_ids)) {
            $books_to_remove[] = $book;
        }
    }
    
    echo "<div class='alert alert-success'>";
    echo "<h5>📋 Plan:</h5>";
    echo "<p>Books to keep: <strong>" . count($books_to_keep) . "</strong></p>";
    echo "<p>Books to remove: <strong>" . count($books_to_remove) . "</strong></p>";
    echo "</div>";
    
    if (count($books_to_remove) > 0) {
        echo "<h4>Books that will be kept (15):</h4>";
        echo "<div class='row mb-3'>";
        foreach ($books_to_keep as $book) {
            echo "<div class='col-md-4 mb-2'>";
            echo "<div class='card'>";
            
            // Show cover image if available
            $cover_path = '';
            if (!empty($book['cover_image'])) {
                $possible_paths = [
                    'uploads/covers/' . $book['cover_image'],
                    'uploads/covers/' . basename($book['cover_image'])
                ];
                
                foreach ($possible_paths as $path) {
                    if (file_exists($path)) {
                        $cover_path = $path;
                        break;
                    }
                }
            }
            
            if ($cover_path) {
                echo "<img src='{$cover_path}' class='card-img-top' style='height: 150px; object-fit: cover;' alt='{$book['title']}'>";
            } else {
                echo "<div class='card-img-top d-flex align-items-center justify-content-center bg-light' style='height: 150px;'>";
                echo "<i class='bi bi-book fs-2 text-secondary'></i>";
                echo "</div>";
            }
            
            echo "<div class='card-body p-2'>";
            echo "<h6 class='card-title'>" . htmlspecialchars($book['title']) . "</h6>";
            echo "<p class='card-text'><small>by " . htmlspecialchars($book['author']) . "</small></p>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        echo "</div>";
        
        echo "<h4>Books that will be removed (" . count($books_to_remove) . "):</h4>";
        echo "<div class='table-responsive mb-3'>";
        echo "<table class='table table-striped table-sm'>";
        echo "<thead class='table-danger'>";
        echo "<tr><th>Title</th><th>Author</th><th>Has Cover</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($books_to_remove as $book) {
            $has_cover = !empty($book['cover_image']) && 
                        (file_exists('uploads/covers/' . $book['cover_image']) || 
                         file_exists('uploads/covers/' . basename($book['cover_image'])));
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($book['title']) . "</td>";
            echo "<td>" . htmlspecialchars($book['author']) . "</td>";
            echo "<td>" . ($has_cover ? '<span class="text-success">Yes</span>' : '<span class="text-danger">No</span>') . "</td>";
            echo "</tr>";
        }
        echo "</tbody></table>";
        echo "</div>";
        
        // Remove the excess books
        $removed_count = 0;
        foreach ($books_to_remove as $book) {
            // Check for active loans
            $loan_check = "SELECT COUNT(*) as count FROM book_loans WHERE book_id = :book_id AND status IN ('borrowed', 'overdue')";
            $loan_stmt = $db->prepare($loan_check);
            $loan_stmt->bindParam(':book_id', $book['id']);
            $loan_stmt->execute();
            $active_loans = $loan_stmt->fetch()['count'];
            
            if ($active_loans > 0) {
                echo "<div class='alert alert-warning'>⚠️ Skipping '{$book['title']}' - has {$active_loans} active loan(s)</div>";
                continue;
            }
            
            // Remove related data first
            $delete_reservations = "DELETE FROM book_reservations WHERE book_id = :book_id";
            $res_stmt = $db->prepare($delete_reservations);
            $res_stmt->bindParam(':book_id', $book['id']);
            $res_stmt->execute();
            
            $delete_loans = "DELETE FROM book_loans WHERE book_id = :book_id";
            $loan_stmt = $db->prepare($delete_loans);
            $loan_stmt->bindParam(':book_id', $book['id']);
            $loan_stmt->execute();
            
            // Remove the book
            $delete_book = "DELETE FROM books WHERE id = :book_id";
            $book_stmt = $db->prepare($delete_book);
            $book_stmt->bindParam(':book_id', $book['id']);
            
            if ($book_stmt->execute()) {
                $removed_count++;
            }
        }
        
        echo "<div class='alert alert-success'><strong>✅ Successfully removed {$removed_count} books!</strong></div>";
        
        // Show final count
        $final_query = "SELECT COUNT(*) as total FROM books";
        $final_stmt = $db->prepare($final_query);
        $final_stmt->execute();
        $final_count = $final_stmt->fetch()['total'];
        
        echo "<div class='alert alert-info'><h4>🎯 Final Result: {$final_count} books remaining in the database</h4></div>";
        
    } else {
        echo "<div class='alert alert-success'>✅ You already have exactly 15 books!</div>";
    }
    
    echo "<div class='mt-3'>";
    echo "<a href='catalog.php' class='btn btn-primary btn-lg me-2'>View Catalog</a>";
    echo "<a href='keep_only_15_books.php' class='btn btn-secondary'>Check Again</a>";
    echo "</div>";
    
} else {
    // Check current status
    echo "<h3>Current Status</h3>";
    
    $query = "SELECT COUNT(*) as total FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $total_books = $stmt->fetch()['total'];
    
    echo "<div class='alert alert-info text-center'>";
    echo "<h4>📚 Current Total: <strong>{$total_books} books</strong></h4>";
    echo "<h5>🎯 Target: <strong>15 books</strong></h5>";
    
    if ($total_books > 15) {
        echo "<p class='text-warning'>Need to remove <strong>" . ($total_books - 15) . " books</strong></p>";
    } elseif ($total_books < 15) {
        echo "<p class='text-info'>You have <strong>" . (15 - $total_books) . " fewer books</strong> than target</p>";
    } else {
        echo "<p class='text-success'>Perfect! You have exactly 15 books!</p>";
    }
    echo "</div>";
    
    if ($total_books > 15) {
        // Show preview of which books will be kept
        $query = "SELECT id, title, author, cover_image FROM books ORDER BY id";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $all_books = $stmt->fetchAll();
        
        $books_with_covers = [];
        $books_without_covers = [];
        
        foreach ($all_books as $book) {
            $has_valid_cover = false;
            
            if (!empty($book['cover_image'])) {
                $cover_paths = [
                    'uploads/covers/' . $book['cover_image'],
                    'uploads/covers/' . basename($book['cover_image'])
                ];
                
                foreach ($cover_paths as $path) {
                    if (file_exists($path)) {
                        $has_valid_cover = true;
                        break;
                    }
                }
            }
            
            if ($has_valid_cover) {
                $books_with_covers[] = $book;
            } else {
                $books_without_covers[] = $book;
            }
        }
        
        echo "<div class='row mb-4'>";
        echo "<div class='col-md-6'>";
        echo "<div class='card text-center'>";
        echo "<div class='card-body'>";
        echo "<h5 class='card-title text-success'>Books WITH Pictures</h5>";
        echo "<h2 class='text-success'>" . count($books_with_covers) . "</h2>";
        echo "<p class='text-muted'>Will be prioritized to keep</p>";
        echo "</div></div></div>";
        
        echo "<div class='col-md-6'>";
        echo "<div class='card text-center'>";
        echo "<div class='card-body'>";
        echo "<h5 class='card-title text-warning'>Books WITHOUT Pictures</h5>";
        echo "<h2 class='text-warning'>" . count($books_without_covers) . "</h2>";
        echo "<p class='text-muted'>Will be removed first</p>";
        echo "</div></div></div>";
        echo "</div>";
        
        echo "<div class='alert alert-warning'>";
        echo "<h5>📋 Reduction Plan:</h5>";
        echo "<p>• <strong>Priority 1:</strong> Keep books with cover images (up to 15)</p>";
        echo "<p>• <strong>Priority 2:</strong> If needed, keep some books without covers to reach 15 total</p>";
        echo "<p>• <strong>Remove:</strong> All excess books beyond 15</p>";
        echo "</div>";
        
        echo "<div class='mt-3'>";
        echo "<a href='keep_only_15_books.php?action=reduce_to_15' class='btn btn-warning btn-lg'>";
        echo "<i class='bi bi-scissors'></i> Reduce to Exactly 15 Books";
        echo "</a>";
        echo "</div>";
    }
}

echo "</div></body></html>";
?>
