<?php
/**
 * Payment Summary Widget
 * Displays quick payment statistics for dashboard inclusion
 */

// Ensure database connection is available
if (!isset($db)) {
    require_once __DIR__ . '/../config/database.php';
    $database = new Database();
    $db = $database->getConnection();
}

// Get today's payment statistics
$today_stats_query = "SELECT
                        COUNT(DISTINCT pt.member_id) as today_paying_members,
                        COUNT(pt.id) as today_transactions,
                        SUM(pt.amount) as today_collected
                      FROM payment_transactions pt
                      WHERE DATE(pt.transaction_date) = CURDATE()
                      AND pt.status = 'completed'";

$today_stats = $db->query($today_stats_query)->fetch();

// Get this month's payment statistics
$month_stats_query = "SELECT
                        COUNT(DISTINCT pt.member_id) as month_paying_members,
                        COUNT(pt.id) as month_transactions,
                        SUM(pt.amount) as month_collected
                      FROM payment_transactions pt
                      WHERE MONTH(pt.transaction_date) = MONTH(CURDATE())
                      AND YEAR(pt.transaction_date) = YEAR(CURDATE())
                      AND pt.status = 'completed'";

$month_stats = $db->query($month_stats_query)->fetch();

// Get outstanding fines
$outstanding_query = "SELECT
                        COUNT(DISTINCT f.member_id) as members_with_fines,
                        SUM(f.amount) as total_outstanding
                      FROM fines f
                      WHERE f.status = 'unpaid'";

$outstanding_stats = $db->query($outstanding_query)->fetch();

// Get recent payments (last 5)
$recent_payments_query = "SELECT
                            pt.receipt_number,
                            pt.amount,
                            pt.transaction_date,
                            pt.payment_method,
                            CONCAT(m.first_name, ' ', m.last_name) as member_name
                          FROM payment_transactions pt
                          JOIN members m ON pt.member_id = m.id
                          WHERE pt.status = 'completed'
                          ORDER BY pt.transaction_date DESC
                          LIMIT 5";

$recent_payments = $db->query($recent_payments_query)->fetchAll();

function formatCurrency($amount) {
    return '₱' . number_format($amount ?? 0, 2);
}

function getPaymentMethodIcon($method) {
    $icons = [
        'cash' => 'bi-cash',
        'gcash' => 'bi-phone',
        'paymaya' => 'bi-credit-card',
        'bank_transfer' => 'bi-bank',
        'credit_card' => 'bi-credit-card-2-front',
        'debit_card' => 'bi-credit-card-2-back'
    ];
    return $icons[$method] ?? 'bi-currency-dollar';
}
?>

<div class="row mb-4">
    <div class="col-12">
        <h5 class="mb-3">
            <i class="bi bi-graph-up me-2"></i>Payment Summary
        </h5>
    </div>
</div>

<!-- Quick Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-0 bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="text-white-50">Today's Collection</h6>
                        <h4 class="mb-0"><?php echo formatCurrency($today_stats['today_collected']); ?></h4>
                        <small class="text-white-50"><?php echo $today_stats['today_transactions'] ?? 0; ?> transactions</small>
                    </div>
                    <div>
                        <i class="bi bi-calendar-day fs-1 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="text-white-50">This Month</h6>
                        <h4 class="mb-0"><?php echo formatCurrency($month_stats['month_collected']); ?></h4>
                        <small class="text-white-50"><?php echo $month_stats['month_paying_members'] ?? 0; ?> members</small>
                    </div>
                    <div>
                        <i class="bi bi-calendar-month fs-1 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="text-dark-50">Outstanding Fines</h6>
                        <h4 class="mb-0"><?php echo formatCurrency($outstanding_stats['total_outstanding']); ?></h4>
                        <small class="text-dark-50"><?php echo $outstanding_stats['members_with_fines'] ?? 0; ?> members</small>
                    </div>
                    <div>
                        <i class="bi bi-exclamation-triangle fs-1 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="text-white-50">Collection Rate</h6>
                        <?php 
                        $total_fines = ($outstanding_stats['total_outstanding'] ?? 0) + ($month_stats['month_collected'] ?? 0);
                        $collection_rate = $total_fines > 0 ? (($month_stats['month_collected'] ?? 0) / $total_fines) * 100 : 0;
                        ?>
                        <h4 class="mb-0"><?php echo number_format($collection_rate, 1); ?>%</h4>
                        <small class="text-white-50">This month</small>
                    </div>
                    <div>
                        <i class="bi bi-percent fs-1 opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Payments -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="bi bi-clock-history me-2"></i>Recent Payments
                </h6>
                <a href="admin/payment_reports.php" class="btn btn-sm btn-outline-primary">
                    View All <i class="bi bi-arrow-right"></i>
                </a>
            </div>
            <div class="card-body p-0">
                <?php if (empty($recent_payments)): ?>
                    <div class="text-center py-4">
                        <i class="bi bi-receipt fs-1 text-muted"></i>
                        <p class="text-muted mt-2 mb-0">No recent payments</p>
                    </div>
                <?php else: ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($recent_payments as $payment): ?>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <i class="<?php echo getPaymentMethodIcon($payment['payment_method']); ?> fs-4 text-primary"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1"><?php echo htmlspecialchars($payment['member_name']); ?></h6>
                                            <small class="text-muted">
                                                <?php echo date('M j, Y g:i A', strtotime($payment['transaction_date'])); ?>
                                                • Receipt: <?php echo htmlspecialchars($payment['receipt_number']); ?>
                                            </small>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <strong class="text-success"><?php echo formatCurrency($payment['amount']); ?></strong><br>
                                        <small class="text-muted"><?php echo ucfirst(str_replace('_', ' ', $payment['payment_method'])); ?></small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card bg-light">
            <div class="card-body text-center">
                <h6 class="mb-3">Quick Actions</h6>
                <div class="btn-group" role="group">
                    <a href="admin/payment_processing.php" class="btn btn-primary">
                        <i class="bi bi-credit-card me-1"></i> Process Payment
                    </a>
                    <a href="admin/payment_reports.php" class="btn btn-outline-primary">
                        <i class="bi bi-file-earmark-bar-graph me-1"></i> View Reports
                    </a>
                    <a href="admin/financial_management.php" class="btn btn-outline-secondary">
                        <i class="bi bi-gear me-1"></i> Manage Fines
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
