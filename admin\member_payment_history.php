<?php
/**
 * Member Payment History
 * View detailed payment history for individual members
 */

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and has financial access
if (!isLoggedIn() || !isStaffWithFinancialAccess()) {
    redirect('../login.php');
    exit;
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get member ID from URL
$member_id = isset($_GET['member_id']) ? (int)$_GET['member_id'] : 0;

if ($member_id <= 0) {
    redirect('payment_reports.php');
    exit;
}

// Get member information
$member_query = "SELECT * FROM members WHERE id = :member_id";
$member_stmt = $db->prepare($member_query);
$member_stmt->bindParam(':member_id', $member_id);
$member_stmt->execute();
$member = $member_stmt->fetch();

if (!$member) {
    redirect('payment_reports.php');
    exit;
}

// Get member's payment history
$payments_query = "SELECT
                    pt.*,
                    b.title as book_title,
                    b.author as book_author,
                    u.username as processed_by_username,
                    f.created_date as fine_created_date,
                    bl.issue_date,
                    bl.due_date,
                    bl.return_date
                   FROM payment_transactions pt
                   JOIN fines f ON pt.fine_id = f.id
                   LEFT JOIN book_loans bl ON f.loan_id = bl.id
                   LEFT JOIN books b ON bl.book_id = b.id
                   LEFT JOIN users u ON pt.processed_by = u.id
                   WHERE pt.member_id = :member_id
                   ORDER BY pt.transaction_date DESC";

$payments_stmt = $db->prepare($payments_query);
$payments_stmt->bindParam(':member_id', $member_id);
$payments_stmt->execute();
$payments = $payments_stmt->fetchAll();

// Get payment statistics for this member
$stats_query = "SELECT
                COUNT(pt.id) as total_transactions,
                SUM(pt.amount) as total_paid,
                AVG(pt.amount) as avg_payment,
                MIN(pt.transaction_date) as first_payment,
                MAX(pt.transaction_date) as last_payment
                FROM payment_transactions pt
                WHERE pt.member_id = :member_id
                AND pt.status = 'completed'";

$stats_stmt = $db->prepare($stats_query);
$stats_stmt->bindParam(':member_id', $member_id);
$stats_stmt->execute();
$stats = $stats_stmt->fetch();

// Get current outstanding fines
$outstanding_query = "SELECT SUM(amount) as outstanding_amount
                      FROM fines
                      WHERE member_id = :member_id
                      AND status = 'unpaid'";

$outstanding_stmt = $db->prepare($outstanding_query);
$outstanding_stmt->bindParam(':member_id', $member_id);
$outstanding_stmt->execute();
$outstanding = $outstanding_stmt->fetch();

function formatCurrency($amount) {
    return '₱' . number_format($amount, 2);
}

function getPaymentMethodBadge($method) {
    $badges = [
        'cash' => 'bg-primary',
        'gcash' => 'bg-success',
        'paymaya' => 'bg-warning',
        'bank_transfer' => 'bg-info',
        'credit_card' => 'bg-secondary',
        'debit_card' => 'bg-dark'
    ];
    return $badges[$method] ?? 'bg-light';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment History - <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        .member-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease-in-out;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .payment-row {
            transition: background-color 0.2s ease;
        }
        .payment-row:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <!-- Back Button -->
                <div class="mb-3">
                    <a href="payment_reports.php" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i> Back to Payment Reports
                    </a>
                </div>

                <!-- Member Header -->
                <div class="member-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-2">
                                <i class="bi bi-person-circle me-2"></i>
                                <?php echo htmlspecialchars($member['first_name'] . ' ' . $member['last_name']); ?>
                            </h1>
                            <p class="mb-1"><i class="bi bi-envelope me-2"></i><?php echo htmlspecialchars($member['email']); ?></p>
                            <?php if ($member['phone']): ?>
                            <p class="mb-1"><i class="bi bi-telephone me-2"></i><?php echo htmlspecialchars($member['phone']); ?></p>
                            <?php endif; ?>
                            <p class="mb-0"><i class="bi bi-calendar me-2"></i>Member since: <?php echo date('F j, Y', strtotime($member['membership_date'])); ?></p>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-<?php echo $member['membership_status'] === 'active' ? 'success' : 'warning'; ?> fs-6 px-3 py-2">
                                <?php echo ucfirst($member['membership_status']); ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Total Payments</h6>
                                        <h3 class="mb-0"><?php echo $stats['total_transactions'] ?? 0; ?></h3>
                                        <small class="text-white-50">Transactions</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-receipt fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Total Paid</h6>
                                        <h3 class="mb-0"><?php echo formatCurrency($stats['total_paid'] ?? 0); ?></h3>
                                        <small class="text-white-50">Amount</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-currency-dollar fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Outstanding</h6>
                                        <h3 class="mb-0"><?php echo formatCurrency($outstanding['outstanding_amount'] ?? 0); ?></h3>
                                        <small class="text-white-50">Unpaid fines</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-exclamation-triangle fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Average Payment</h6>
                                        <h3 class="mb-0"><?php echo formatCurrency($stats['avg_payment'] ?? 0); ?></h3>
                                        <small class="text-white-50">Per transaction</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-calculator fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment History -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-clock-history me-2"></i>Payment History
                            <span class="badge bg-primary"><?php echo count($payments); ?> records</span>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($payments)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-receipt fs-1 text-muted"></i>
                                <p class="text-muted mt-3">No payment history found for this member.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Receipt #</th>
                                            <th>Date & Time</th>
                                            <th>Book</th>
                                            <th>Amount</th>
                                            <th>Payment Method</th>
                                            <th>Reference</th>
                                            <th>Processed By</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($payments as $payment): ?>
                                            <tr class="payment-row">
                                                <td>
                                                    <strong class="text-primary"><?php echo htmlspecialchars($payment['receipt_number']); ?></strong>
                                                </td>
                                                <td>
                                                    <?php echo date('M j, Y', strtotime($payment['transaction_date'])); ?><br>
                                                    <small class="text-muted"><?php echo date('g:i A', strtotime($payment['transaction_date'])); ?></small>
                                                </td>
                                                <td>
                                                    <?php if ($payment['book_title']): ?>
                                                        <strong><?php echo htmlspecialchars($payment['book_title']); ?></strong><br>
                                                        <small class="text-muted">by <?php echo htmlspecialchars($payment['book_author']); ?></small>
                                                    <?php else: ?>
                                                        <span class="text-muted">N/A</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong class="text-success"><?php echo formatCurrency($payment['amount']); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge <?php echo getPaymentMethodBadge($payment['payment_method']); ?>">
                                                        <?php echo ucfirst(str_replace('_', ' ', $payment['payment_method'])); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($payment['payment_reference']): ?>
                                                        <code><?php echo htmlspecialchars($payment['payment_reference']); ?></code>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small><?php echo htmlspecialchars($payment['processed_by_username']); ?></small>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                                            onclick="viewReceipt('<?php echo $payment['receipt_number']; ?>')">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function viewReceipt(receiptNumber) {
            window.open('payment_receipt.php?receipt=' + receiptNumber, '_blank', 'width=800,height=600');
        }
    </script>
</body>
</html>
