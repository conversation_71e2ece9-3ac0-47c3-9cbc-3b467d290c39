<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Set up pagination
$records_per_page = 12;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $records_per_page;

// Get search and filter parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$category = isset($_GET['category']) ? trim($_GET['category']) : '';

// Build query based on search and filter
$params = [];
$where_clauses = [];

if (!empty($search)) {
    $where_clauses[] = "(title LIKE :search OR author LIKE :search OR isbn LIKE :search)";
    $params[':search'] = "%$search%";
}

if (!empty($category)) {
    $where_clauses[] = "category = :category";
    $params[':category'] = $category;
}

// Only show books with available copies
$where_clauses[] = "available_quantity > 0";

$where_clause = !empty($where_clauses) ? "WHERE " . implode(" AND ", $where_clauses) : "";

// Count total records for pagination
$count_query = "SELECT COUNT(*) as total FROM books $where_clause";
$count_stmt = $db->prepare($count_query);
foreach ($params as $key => $value) {
    $count_stmt->bindValue($key, $value);
}
$count_stmt->execute();
$total_rows = $count_stmt->fetch()['total'];
$total_pages = ceil($total_rows / $records_per_page);

// Get books with pagination
$query = "SELECT * FROM books $where_clause ORDER BY title ASC LIMIT :offset, :records_per_page";
$stmt = $db->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->bindValue(':records_per_page', $records_per_page, PDO::PARAM_INT);
$stmt->execute();
$books = $stmt->fetchAll();

// Get all categories for filter
$category_query = "SELECT DISTINCT category FROM books WHERE category IS NOT NULL AND category != '' ORDER BY category";
$category_stmt = $db->prepare($category_query);
$category_stmt->execute();
$categories = $category_stmt->fetchAll();

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Catalog - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .catalog-container {
            max-width: 1200px;
            margin: 20px auto;
        }
        .card {
            height: 100%;
            transition: transform 0.3s;
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .card-img-top {
            height: 250px;
            object-fit: cover;
        }
        .card-img-placeholder {
            height: 250px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #e9ecef;
        }
        .navbar {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="home.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo isMemberLoggedIn() ? 'member_dashboard.php' : 'home.php'; ?>">
                            <?php echo isMemberLoggedIn() ? 'Dashboard' : 'Home'; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="catalog.php">Book Catalog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.php">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">Contact</a>
                    </li>
                </ul>
                <div class="d-flex">
                    <?php if (isLoggedIn()): ?>
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle me-1"></i><?php echo h($_SESSION['username']); ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton">
                                <li><a class="dropdown-item" href="admin/dashboard.php">Admin Dashboard</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                            </ul>
                        </div>
                    <?php elseif (isMemberLoggedIn()): ?>
                        <div class="dropdown">
                            <button class="btn btn-outline-light dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="bi bi-person-circle me-1"></i><?php echo h($_SESSION['member_name']); ?>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton">
                                <li><a class="dropdown-item" href="member_dashboard.php">My Dashboard</a></li>
                                <li><a class="dropdown-item" href="member_profile.php">My Profile</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <a href="login.php" class="btn btn-outline-light me-2">Login</a>
                        <a href="register.php" class="btn btn-primary">Register</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>

    <div class="catalog-container">
        <h2 class="mb-4">Book Catalog</h2>

        <!-- Search and Filter Form -->
        <div class="card mb-4">
            <div class="card-body">
                <form action="<?php echo h($_SERVER['PHP_SELF']); ?>" method="get" class="row g-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" name="search" placeholder="Search by title, author, or ISBN" value="<?php echo h($search); ?>">
                            <button class="btn btn-primary" type="submit">Search</button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <select class="form-select" name="category" onchange="this.form.submit()">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $cat): ?>
                                <option value="<?php echo h($cat['category']); ?>" <?php echo $category === $cat['category'] ? 'selected' : ''; ?>>
                                    <?php echo h($cat['category']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <?php if (!empty($search) || !empty($category)): ?>
                            <a href="catalog.php" class="btn btn-outline-secondary w-100">Clear Filters</a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>

        <!-- Books Grid -->
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4 mb-4">
            <?php if (count($books) > 0): ?>
                <?php foreach ($books as $book): ?>
                    <div class="col">
                        <div class="card h-100">
                            <?php if (!empty($book['cover_image'])): ?>
                                <?php
                                // Check if the cover_image is a URL or a local file
                                if (strpos($book['cover_image'], 'http') === 0) {
                                    $image_src = $book['cover_image'];
                                } else {
                                    // Clean the filename and construct proper path
                                    $clean_filename = basename($book['cover_image']);
                                    $image_src = 'uploads/covers/' . $clean_filename;

                                    // Check if file exists, if not try alternative paths
                                    if (!file_exists($image_src)) {
                                        $possible_paths = [
                                            './uploads/covers/' . $clean_filename,
                                            'uploads/covers/' . $book['cover_image'], // Original value
                                            './uploads/covers/' . $book['cover_image']
                                        ];

                                        foreach ($possible_paths as $path) {
                                            if (file_exists($path)) {
                                                $image_src = $path;
                                                break;
                                            }
                                        }
                                    }
                                }
                                ?>
                                <img src="<?php echo h($image_src); ?>" class="card-img-top" alt="<?php echo h($book['title']); ?>"
                                     style="height: 250px; object-fit: cover;"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="card-img-placeholder d-flex align-items-center justify-content-center bg-light"
                                     style="height: 250px; display: none;">
                                    <i class="bi bi-book fs-1 text-secondary"></i>
                                </div>
                            <?php else: ?>
                                <div class="card-img-placeholder d-flex align-items-center justify-content-center bg-light"
                                     style="height: 250px;">
                                    <i class="bi bi-book fs-1 text-secondary"></i>
                                </div>
                            <?php endif; ?>
                            <div class="card-body">
                                <h5 class="card-title"><?php echo h($book['title']); ?></h5>
                                <h6 class="card-subtitle mb-2 text-muted"><?php echo h($book['author']); ?></h6>
                                <p class="card-text">
                                    <small class="text-muted">
                                        <?php if (!empty($book['category'])): ?>
                                            <span class="badge bg-secondary"><?php echo h($book['category']); ?></span>
                                        <?php endif; ?>
                                        <?php if (!empty($book['publication_year'])): ?>
                                            <span class="badge bg-light text-dark"><?php echo h($book['publication_year']); ?></span>
                                        <?php endif; ?>
                                    </small>
                                </p>
                                <p class="card-text">
                                    <small class="text-success">
                                        Available: <?php echo h($book['available_quantity']); ?> of <?php echo h($book['quantity']); ?>
                                    </small>
                                </p>
                            </div>
                            <div class="card-footer bg-transparent border-top-0">
                                <a href="book_details.php?id=<?php echo h($book['id']); ?>" class="btn btn-sm btn-primary w-100">View Details</a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="col-12">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        No books found matching your criteria. Please try a different search or filter.
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo $page <= 1 ? '#' : '?page='.($page-1).(!empty($search) ? '&search='.urlencode($search) : '').(!empty($category) ? '&category='.urlencode($category) : ''); ?>">Previous</a>
                    </li>

                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?><?php echo !empty($category) ? '&category='.urlencode($category) : ''; ?>"><?php echo $i; ?></a>
                        </li>
                    <?php endfor; ?>

                    <li class="page-item <?php echo $page >= $total_pages ? 'disabled' : ''; ?>">
                        <a class="page-link" href="<?php echo $page >= $total_pages ? '#' : '?page='.($page+1).(!empty($search) ? '&search='.urlencode($search) : '').(!empty($category) ? '&category='.urlencode($category) : ''); ?>">Next</a>
                    </li>
                </ul>
            </nav>
        <?php endif; ?>
    </div>

    <?php include 'includes/footer.php'; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
