<?php
/**
 * Database Configuration
 */
class Database {
    private $host = "localhost";
    private $db_name = "lms_db";
    private $username = "root";
    private $password = "password1234";
    private $conn;

    /**
     * Get the database connection
     * @return PDO|null
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password
            );
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            error_log("Database Connection Error: " . $e->getMessage());
            throw new Exception("Database connection failed: " . $e->getMessage());
        }

        return $this->conn;
    }

    /**
     * Test database connection
     * @return array
     */
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            if ($conn) {
                return ['success' => true, 'message' => 'Database connection successful'];
            } else {
                return ['success' => false, 'message' => 'Failed to establish database connection'];
            }
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
}
?>
