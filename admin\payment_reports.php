<?php
/**
 * Payment Reports - Track Members Who Paid Fines
 * Generate reports and view payment history
 */

session_start();
require_once '../config/database.php';
require_once '../config/config.php';
require_once '../includes/functions.php';

// Check if user is logged in and has financial access (admin or librarian)
if (!isLoggedIn() || !isStaffWithFinancialAccess()) {
    redirect('../login.php');
    exit;
}

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get filter parameters
$date_from = isset($_GET['date_from']) ? $_GET['date_from'] : date('Y-m-01'); // First day of current month
$date_to = isset($_GET['date_to']) ? $_GET['date_to'] : date('Y-m-d'); // Today
$payment_method = isset($_GET['payment_method']) ? $_GET['payment_method'] : '';
$export = isset($_GET['export']) ? $_GET['export'] : false;

// Get payment statistics
$stats_query = "SELECT
                    COUNT(DISTINCT pt.member_id) as total_paying_members,
                    COUNT(pt.id) as total_transactions,
                    SUM(pt.amount) as total_amount_collected,
                    AVG(pt.amount) as average_payment,
                    COUNT(CASE WHEN pt.payment_method = 'cash' THEN 1 END) as cash_payments,
                    COUNT(CASE WHEN pt.payment_method = 'gcash' THEN 1 END) as gcash_payments,
                    COUNT(CASE WHEN pt.payment_method = 'paymaya' THEN 1 END) as paymaya_payments,
                    COUNT(CASE WHEN pt.payment_method = 'bank_transfer' THEN 1 END) as bank_payments,
                    COUNT(CASE WHEN pt.payment_method = 'credit_card' THEN 1 END) as credit_payments,
                    COUNT(CASE WHEN pt.payment_method = 'debit_card' THEN 1 END) as debit_payments
                FROM payment_transactions pt
                WHERE pt.transaction_date BETWEEN :date_from AND :date_to
                AND pt.status = 'completed'";

if ($payment_method) {
    $stats_query .= " AND pt.payment_method = :payment_method";
}

$stats_stmt = $db->prepare($stats_query);
$stats_stmt->bindParam(':date_from', $date_from);
$stats_stmt->bindParam(':date_to', $date_to);
if ($payment_method) {
    $stats_stmt->bindParam(':payment_method', $payment_method);
}
$stats_stmt->execute();
$stats = $stats_stmt->fetch();

// Get detailed payment records
$payments_query = "SELECT
                    pt.*,
                    CONCAT(m.first_name, ' ', m.last_name) as member_name,
                    m.email as member_email,
                    m.phone as member_phone,
                    b.title as book_title,
                    u.username as processed_by_username,
                    f.created_date as fine_created_date
                   FROM payment_transactions pt
                   JOIN members m ON pt.member_id = m.id
                   JOIN fines f ON pt.fine_id = f.id
                   LEFT JOIN book_loans bl ON f.loan_id = bl.id
                   LEFT JOIN books b ON bl.book_id = b.id
                   LEFT JOIN users u ON pt.processed_by = u.id
                   WHERE pt.transaction_date BETWEEN :date_from AND :date_to
                   AND pt.status = 'completed'";

if ($payment_method) {
    $payments_query .= " AND pt.payment_method = :payment_method";
}

$payments_query .= " ORDER BY pt.transaction_date DESC";

$payments_stmt = $db->prepare($payments_query);
$payments_stmt->bindParam(':date_from', $date_from);
$payments_stmt->bindParam(':date_to', $date_to);
if ($payment_method) {
    $payments_stmt->bindParam(':payment_method', $payment_method);
}
$payments_stmt->execute();
$payments = $payments_stmt->fetchAll();

// Payment method options
$payment_methods = [
    '' => 'All Payment Methods',
    'cash' => 'Cash',
    'gcash' => 'GCash',
    'paymaya' => 'PayMaya',
    'bank_transfer' => 'Bank Transfer',
    'credit_card' => 'Credit Card',
    'debit_card' => 'Debit Card'
];

function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

function formatCurrency($amount) {
    return '₱' . number_format($amount, 2);
}

function getPaymentMethodBadge($method) {
    $badges = [
        'cash' => 'bg-primary',
        'gcash' => 'bg-success',
        'paymaya' => 'bg-warning',
        'bank_transfer' => 'bg-info',
        'credit_card' => 'bg-secondary',
        'debit_card' => 'bg-dark'
    ];
    return $badges[$method] ?? 'bg-light';
}

// Handle CSV export
if ($export === 'csv') {
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="payment_report_' . date('Y-m-d') . '.csv"');

    $output = fopen('php://output', 'w');

    // CSV headers
    fputcsv($output, [
        'Receipt Number', 'Transaction Date', 'Member Name', 'Member Email',
        'Book Title', 'Amount', 'Payment Method', 'Reference', 'Processed By'
    ]);

    // CSV data
    foreach ($payments as $payment) {
        fputcsv($output, [
            $payment['receipt_number'],
            date('Y-m-d H:i:s', strtotime($payment['transaction_date'])),
            $payment['member_name'],
            $payment['member_email'],
            $payment['book_title'] ?? 'N/A',
            $payment['amount'],
            ucfirst(str_replace('_', ' ', $payment['payment_method'])),
            $payment['payment_reference'] ?? 'N/A',
            $payment['processed_by_username']
        ]);
    }

    fclose($output);
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Reports - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease-in-out;
        }
        .stats-card:hover {
            transform: translateY(-2px);
        }
        .payment-row {
            transition: background-color 0.2s ease;
        }
        .payment-row:hover {
            background-color: #f8f9fa;
        }
        .filter-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .method-stat {
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <div class="container-fluid">
        <div class="row">
            <?php include '../includes/sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-file-earmark-bar-graph me-2"></i>Payment Reports
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="payment_processing.php" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-credit-card me-1"></i> Process Payments
                            </a>
                            <a href="?<?php echo http_build_query(array_merge($_GET, ['export' => 'csv'])); ?>" class="btn btn-sm btn-outline-success">
                                <i class="bi bi-download me-1"></i> Export CSV
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card filter-card mb-4">
                    <div class="card-body">
                        <h5 class="card-title text-white mb-3">
                            <i class="bi bi-funnel me-2"></i>Filter Reports
                        </h5>
                        <form method="get" class="row g-3">
                            <div class="col-md-3">
                                <label for="date_from" class="form-label text-white">From Date:</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo h($date_from); ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="date_to" class="form-label text-white">To Date:</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo h($date_to); ?>">
                            </div>
                            <div class="col-md-4">
                                <label for="payment_method" class="form-label text-white">Payment Method:</label>
                                <select class="form-select" id="payment_method" name="payment_method">
                                    <?php foreach ($payment_methods as $value => $label): ?>
                                        <option value="<?php echo $value; ?>" <?php echo $payment_method === $value ? 'selected' : ''; ?>>
                                            <?php echo $label; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label text-white">&nbsp;</label>
                                <button type="submit" class="btn btn-light w-100">
                                    <i class="bi bi-search me-1"></i> Filter
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Paying Members</h6>
                                        <h3 class="mb-0"><?php echo $stats['total_paying_members'] ?? 0; ?></h3>
                                        <small class="text-white-50">Unique members</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-people fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-primary">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Total Collected</h6>
                                        <h3 class="mb-0"><?php echo formatCurrency($stats['total_amount_collected'] ?? 0); ?></h3>
                                        <small class="text-white-50">Revenue</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-currency-dollar fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Transactions</h6>
                                        <h3 class="mb-0"><?php echo $stats['total_transactions'] ?? 0; ?></h3>
                                        <small class="text-white-50">Payments processed</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-receipt fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card text-white bg-warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="text-white-50">Average Payment</h6>
                                        <h3 class="mb-0"><?php echo formatCurrency($stats['average_payment'] ?? 0); ?></h3>
                                        <small class="text-white-50">Per transaction</small>
                                    </div>
                                    <div>
                                        <i class="bi bi-calculator fs-1"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Method Breakdown -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-pie-chart me-2"></i>Payment Method Distribution
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="paymentMethodChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-list-check me-2"></i>Method Breakdown
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="method-stat d-flex justify-content-between mb-2">
                                    <span><i class="bi bi-cash text-primary"></i> Cash:</span>
                                    <strong><?php echo $stats['cash_payments'] ?? 0; ?></strong>
                                </div>
                                <div class="method-stat d-flex justify-content-between mb-2">
                                    <span><i class="bi bi-phone text-success"></i> GCash:</span>
                                    <strong><?php echo $stats['gcash_payments'] ?? 0; ?></strong>
                                </div>
                                <div class="method-stat d-flex justify-content-between mb-2">
                                    <span><i class="bi bi-credit-card text-warning"></i> PayMaya:</span>
                                    <strong><?php echo $stats['paymaya_payments'] ?? 0; ?></strong>
                                </div>
                                <div class="method-stat d-flex justify-content-between mb-2">
                                    <span><i class="bi bi-bank text-info"></i> Bank Transfer:</span>
                                    <strong><?php echo $stats['bank_payments'] ?? 0; ?></strong>
                                </div>
                                <div class="method-stat d-flex justify-content-between mb-2">
                                    <span><i class="bi bi-credit-card-2-front text-secondary"></i> Credit Card:</span>
                                    <strong><?php echo $stats['credit_payments'] ?? 0; ?></strong>
                                </div>
                                <div class="method-stat d-flex justify-content-between">
                                    <span><i class="bi bi-credit-card-2-back text-dark"></i> Debit Card:</span>
                                    <strong><?php echo $stats['debit_payments'] ?? 0; ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Transactions List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-receipt me-2"></i>Payment Transactions
                            <span class="badge bg-primary"><?php echo count($payments); ?> records</span>
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($payments)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-receipt fs-1 text-muted"></i>
                                <p class="text-muted mt-3">No payment transactions found for the selected period.</p>
                                <a href="payment_processing.php" class="btn btn-primary">
                                    <i class="bi bi-credit-card me-1"></i> Process New Payment
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Receipt #</th>
                                            <th>Date & Time</th>
                                            <th>Member</th>
                                            <th>Book</th>
                                            <th>Amount</th>
                                            <th>Payment Method</th>
                                            <th>Reference</th>
                                            <th>Processed By</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($payments as $payment): ?>
                                            <tr class="payment-row">
                                                <td>
                                                    <strong class="text-primary"><?php echo h($payment['receipt_number']); ?></strong>
                                                </td>
                                                <td>
                                                    <?php echo date('M j, Y', strtotime($payment['transaction_date'])); ?><br>
                                                    <small class="text-muted"><?php echo date('g:i A', strtotime($payment['transaction_date'])); ?></small>
                                                </td>
                                                <td>
                                                    <strong><?php echo h($payment['member_name']); ?></strong><br>
                                                    <small class="text-muted"><?php echo h($payment['member_email']); ?></small><br>
                                                    <a href="member_payment_history.php?member_id=<?php echo $payment['member_id']; ?>" class="btn btn-sm btn-outline-info mt-1">
                                                        <i class="bi bi-clock-history"></i> History
                                                    </a>
                                                </td>
                                                <td><?php echo h($payment['book_title'] ?? 'N/A'); ?></td>
                                                <td>
                                                    <strong class="text-success"><?php echo formatCurrency($payment['amount']); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge <?php echo getPaymentMethodBadge($payment['payment_method']); ?>">
                                                        <?php echo ucfirst(str_replace('_', ' ', $payment['payment_method'])); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($payment['payment_reference']): ?>
                                                        <code><?php echo h($payment['payment_reference']); ?></code>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small><?php echo h($payment['processed_by_username']); ?></small>
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-outline-primary"
                                                            onclick="viewReceipt('<?php echo $payment['receipt_number']; ?>')">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Payment Method Distribution Chart
        const ctx = document.getElementById('paymentMethodChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Cash', 'GCash', 'PayMaya', 'Bank Transfer', 'Credit Card', 'Debit Card'],
                datasets: [{
                    data: [
                        <?php echo $stats['cash_payments'] ?? 0; ?>,
                        <?php echo $stats['gcash_payments'] ?? 0; ?>,
                        <?php echo $stats['paymaya_payments'] ?? 0; ?>,
                        <?php echo $stats['bank_payments'] ?? 0; ?>,
                        <?php echo $stats['credit_payments'] ?? 0; ?>,
                        <?php echo $stats['debit_payments'] ?? 0; ?>
                    ],
                    backgroundColor: [
                        '#0d6efd',
                        '#198754',
                        '#ffc107',
                        '#0dcaf0',
                        '#6c757d',
                        '#212529'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        function viewReceipt(receiptNumber) {
            // Open receipt in new window
            window.open('payment_receipt.php?receipt=' + receiptNumber, '_blank', 'width=800,height=600');
        }
    </script>
</body>
</html>