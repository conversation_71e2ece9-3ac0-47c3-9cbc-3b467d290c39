<?php
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';
require_once 'config/config.php';
require_once 'includes/functions.php';

// Check if member is logged in
if (!isset($_SESSION['member_id'])) {
    // Try to auto-login for testing purposes
    try {
        $database = new Database();
        $db = $database->getConnection();

        // Get any active member for testing
        $query = "SELECT * FROM members WHERE membership_status = 'active' LIMIT 1";
        $stmt = $db->prepare($query);
        $stmt->execute();
        $test_member = $stmt->fetch();

        if ($test_member) {
            $_SESSION['member_id'] = $test_member['id'];
            $_SESSION['member_name'] = $test_member['first_name'] . ' ' . $test_member['last_name'];
            $_SESSION['member_email'] = $test_member['email'];

            // Show debug message
            echo '<div class="alert alert-warning">Auto-logged in as test member: ' . htmlspecialchars($test_member['first_name']) . '</div>';
        } else {
            header('Location: login.php');
            exit;
        }
    } catch (Exception $e) {
        header('Location: login.php');
        exit;
    }
}

// Get member ID from session
$member_id = $_SESSION['member_id'];

// Connect to database
$database = new Database();
$db = $database->getConnection();

// Get member details
$query = "SELECT * FROM members WHERE id = :member_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$member = $stmt->fetch();

// Check if the member is signed in with Google
$is_google_user = !empty($member['google_id']);

// Get current loans
$query = "SELECT bl.*, b.title, b.author, b.isbn, b.cover_image
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id AND bl.status != 'returned'
          ORDER BY bl.due_date ASC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$current_loans = $stmt->fetchAll();

// Get loan history
$query = "SELECT bl.*, b.title, b.author, b.isbn
          FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id AND bl.status = 'returned'
          ORDER BY bl.return_date DESC
          LIMIT 5";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$loan_history = $stmt->fetchAll();

// Get reservations
$query = "SELECT br.*, b.title, b.author, b.isbn
          FROM book_reservations br
          JOIN books b ON br.book_id = b.id
          WHERE br.member_id = :member_id AND br.status IN ('pending', 'ready')
          ORDER BY br.reservation_date DESC";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$reservations = $stmt->fetchAll();

// Calculate total fines
$query = "SELECT SUM(fine) as total_fine FROM book_loans
          WHERE member_id = :member_id";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$total_fine = $stmt->fetch()['total_fine'] ?? 0;

// Get reading statistics
$query = "SELECT COUNT(*) as books_read_this_year FROM book_loans
          WHERE member_id = :member_id AND status = 'returned'
          AND YEAR(return_date) = YEAR(CURDATE())";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$books_read_this_year = $stmt->fetch()['books_read_this_year'] ?? 0;

// Get favorite genre
$query = "SELECT b.category, COUNT(*) as count FROM book_loans bl
          JOIN books b ON bl.book_id = b.id
          WHERE bl.member_id = :member_id AND bl.status = 'returned'
          GROUP BY b.category ORDER BY count DESC LIMIT 1";
$stmt = $db->prepare($query);
$stmt->bindParam(':member_id', $member_id);
$stmt->execute();
$favorite_genre = $stmt->fetch()['category'] ?? 'Not available';

// Get wishlist count (handle missing table gracefully)
$wishlist_count = 0;
try {
    $query = "SELECT COUNT(*) as wishlist_count FROM member_wishlist WHERE member_id = :member_id";
    $stmt = $db->prepare($query);
    $stmt->bindParam(':member_id', $member_id);
    $stmt->execute();
    $wishlist_count = $stmt->fetch()['wishlist_count'] ?? 0;
} catch (PDOException $e) {
    // Table doesn't exist, continue with 0
    $wishlist_count = 0;
}

// Check for overdue books
$overdue_count = 0;
foreach ($current_loans as $loan) {
    if (strtotime($loan['due_date']) < time()) {
        $overdue_count++;
    }
}

// Function to sanitize output
function h($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Dashboard - Library Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .dashboard-container {
            max-width: 1200px;
            margin: 20px auto;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            font-weight: bold;
        }
        .navbar {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .book-cover {
            width: 80px;
            height: 120px;
            object-fit: cover;
            border: 1px solid #ddd;
        }
        .overdue {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Add this right after the opening <body> tag, before the navigation -->
    <style>
        /* Enhanced dashboard styles */
        .dashboard-container {
            max-width: 1200px;
            margin: 20px auto;
        }
        .welcome-card {
            background: linear-gradient(to right, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .stats-card {
            text-align: center;
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .stats-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #007bff;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            border: none;
            margin-bottom: 20px;
        }
        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            font-weight: bold;
        }
        .book-cover {
            width: 80px;
            height: 120px;
            object-fit: cover;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
    </style>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-book me-2"></i>Library Management System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="member_dashboard.php">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="catalog.php">Book Catalog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="alert('Borrow Books feature coming soon!')">
                            <i class="bi bi-plus-circle me-1"></i>Borrow Books
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="alert('My Loans feature coming soon!')">My Loans</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="alert('Wishlist feature coming soon!')">
                            <i class="bi bi-heart me-1"></i>Wishlist
                            <?php if ($wishlist_count > 0): ?>
                                <span class="badge bg-danger ms-1"><?php echo $wishlist_count; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                </ul>

                <!-- Quick Search Bar -->
                <form class="d-flex me-3" action="catalog.php" method="GET">
                    <input class="form-control me-2" type="search" name="search" placeholder="Search books..." aria-label="Search" style="width: 200px;">
                    <button class="btn btn-outline-light" type="submit">
                        <i class="bi bi-search"></i>
                    </button>
                </form>
                <div class="d-flex">
                    <div class="dropdown">
                        <button class="btn btn-outline-light dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle me-1"></i><?php echo h($_SESSION['member_name']); ?>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton">
                            <li><a class="dropdown-item" href="#" onclick="alert('Profile feature coming soon!')">My Profile</a></li>
                            <?php if (!$is_google_user): ?>
                            <li><a class="dropdown-item" href="#" onclick="alert('Google account linking coming soon!')">
                                <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="16" height="16" class="me-2">
                                Link Google Account
                            </a></li>
                            <?php else: ?>
                            <li>
                                <div class="dropdown-item d-flex align-items-center">
                                    <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="16" height="16" class="me-2">
                                    <span class="text-muted">Signed in with Google</span>
                                </div>
                            </li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="logout.php">Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <?php
        // Log member dashboard access
        error_log("Member Dashboard: Member ID: " . $_SESSION['member_id'] . ", Name: " . $_SESSION['member_name']);

        // Check for streamlined Google sign-in
        if (isset($_SESSION['streamlined_google_signin']) && $_SESSION['streamlined_google_signin']) {
            // Clear the streamlined sign-in flag
            unset($_SESSION['streamlined_google_signin']);

            // Show a small notification for streamlined sign-in
            error_log("Member Dashboard: Streamlined Google sign-in completed");
            ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert" style="background-color: #f8f9fa; color: #3c4043; border-color: #dadce0;">
                <div class="d-flex align-items-center">
                    <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" width="18" height="18" class="me-2">
                    <span>You've been signed in with your Google account</span>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php
        }
        // Check for regular Google login
        elseif (isset($_SESSION['google_login']) && $_SESSION['google_login']) { ?>
            <?php if (isset($_SESSION['new_google_user']) && $_SESSION['new_google_user']) { ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-google me-2"></i>
                    <strong>Welcome!</strong> Your account has been created using your Google account information.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php
                error_log("Member Dashboard: New Google user welcome message shown");
                unset($_SESSION['new_google_user']);
                ?>
            <?php } else { ?>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <i class="bi bi-google me-2"></i>
                    <strong>Welcome back!</strong> You've been signed in with your Google account.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php error_log("Member Dashboard: Returning Google user welcome message shown"); ?>
            <?php } ?>
            <?php unset($_SESSION['google_login']); ?>
        <?php } ?>

        <?php if (isset($_GET['login'])) { ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="bi bi-info-circle me-2"></i>
                <strong>Debug Info:</strong> Login timestamp: <?php echo h($_GET['login']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php } ?>

        <!-- Enhanced Alert System -->
        <?php if ($overdue_count > 0): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <strong>Overdue Books Alert!</strong> You have <?php echo $overdue_count; ?> overdue book(s).
                Please return them as soon as possible to avoid additional fines.
                <a href="member/return_book.php" class="btn btn-sm btn-outline-danger ms-2">Return Books</a>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php
        // Check for books due soon (within 3 days)
        $due_soon_count = 0;
        foreach ($current_loans as $loan) {
            $days_until_due = (strtotime($loan['due_date']) - time()) / (60 * 60 * 24);
            if ($days_until_due > 0 && $days_until_due <= 3) {
                $due_soon_count++;
            }
        }
        if ($due_soon_count > 0): ?>
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="bi bi-clock me-2"></i>
                <strong>Due Soon!</strong> You have <?php echo $due_soon_count; ?> book(s) due within the next 3 days.
                <a href="member/my_loans.php" class="btn btn-sm btn-outline-warning ms-2">View Details</a>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($total_fine > 0): ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <i class="bi bi-credit-card me-2"></i>
                        <strong>Outstanding Fines:</strong> You have $<?php echo number_format($total_fine, 2); ?> in unpaid fines.
                    </div>
                    <div>
                        <a href="#" class="btn btn-sm btn-outline-info me-2" data-bs-toggle="modal" data-bs-target="#payFineModal">
                            <i class="bi bi-credit-card me-1"></i>Quick Pay
                        </a>
                        <button class="btn btn-sm btn-info" onclick="alert('Payment feature coming soon!')">
                            <i class="bi bi-arrow-right me-1"></i>Full Payment Page
                        </button>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-12">
                <h2 class="mb-4">Member Dashboard</h2>

                <!-- Add this right after the <h2 class="mb-4">Member Dashboard</h2> line -->
                <div class="welcome-card">
                    <div class="row">
                        <div class="col-md-8">
                            <h4>Welcome back, <?php echo h($member['first_name']); ?>!</h4>
                            <p class="text-muted">Member since <?php echo formatDate($member['membership_date']); ?></p>
                        </div>
                        <div class="col-md-4 text-end">
                            <?php if ($total_fine > 0): ?>
                                <p class="text-danger mb-2">
                                    <i class="bi bi-exclamation-circle"></i>
                                    Outstanding Fine: $<?php echo number_format($total_fine, 2); ?>
                                </p>
                            <?php else: ?>
                                <p class="text-success mb-2">
                                    <i class="bi bi-check-circle"></i> No outstanding fines
                                </p>
                            <?php endif; ?>
                            <button class="btn btn-outline-primary btn-sm" onclick="alert('Profile editing feature coming soon!')">
                                <i class="bi bi-pencil"></i> Edit Profile
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-card">
                            <div class="stats-icon">
                                <i class="bi bi-book"></i>
                            </div>
                            <h3><?php echo count($current_loans); ?></h3>
                            <p class="text-muted">Current Loans</p>
                            <?php if ($overdue_count > 0): ?>
                                <small class="text-danger"><?php echo $overdue_count; ?> overdue</small>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-card">
                            <div class="stats-icon" style="color: #6c757d;">
                                <i class="bi bi-bookmark"></i>
                            </div>
                            <h3><?php echo count($reservations); ?></h3>
                            <p class="text-muted">Reservations</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-card">
                            <div class="stats-icon" style="color: #28a745;">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <h3><?php echo $books_read_this_year; ?></h3>
                            <p class="text-muted">Books Read This Year</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-card">
                            <div class="stats-icon" style="color: #dc3545;">
                                <i class="bi bi-heart-fill"></i>
                            </div>
                            <h3><?php echo $wishlist_count; ?></h3>
                            <p class="text-muted">Wishlist Items</p>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Row -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="bi bi-lightning-fill me-2"></i>Quick Actions</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-primary w-100" onclick="alert('Borrow Books feature coming soon!')">
                                            <i class="bi bi-plus-circle me-2"></i>Borrow Books
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-success w-100" onclick="alert('Return Books feature coming soon!')">
                                            <i class="bi bi-arrow-return-left me-2"></i>Return Books
                                            <?php if (count($current_loans) > 0): ?>
                                                <span class="badge bg-light text-dark ms-1"><?php echo count($current_loans); ?></span>
                                            <?php endif; ?>
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <a href="catalog.php" class="btn btn-info w-100">
                                            <i class="bi bi-search me-2"></i>Browse Catalog
                                        </a>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-outline-danger w-100" onclick="alert('Wishlist feature coming soon!')">
                                            <i class="bi bi-heart me-2"></i>My Wishlist
                                            <?php if ($wishlist_count > 0): ?>
                                                <span class="badge bg-danger ms-1"><?php echo $wishlist_count; ?></span>
                                            <?php endif; ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Overdue Books Section -->
                <?php
                $overdue_loans_with_fines = array_filter($current_loans, function($loan) {
                    return strtotime($loan['due_date']) < time() && ($loan['fine'] ?? 0) > 0;
                });
                ?>
                <?php if (count($overdue_loans_with_fines) > 0): ?>
                <div class="card mb-4 border-danger">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>Overdue Books with Fines
                            <span class="badge bg-light text-danger ms-2"><?php echo count($overdue_loans_with_fines); ?></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Action Required:</strong> Please pay your fines and return these books as soon as possible.
                        </div>

                        <div class="row">
                            <?php foreach ($overdue_loans_with_fines as $loan): ?>
                                <?php
                                $days_overdue = floor((time() - strtotime($loan['due_date'])) / (60 * 60 * 24));
                                $book_fine = $loan['fine'] ?? ($days_overdue * 1.00);
                                ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card border-warning">
                                        <div class="card-body">
                                            <h6 class="card-title text-danger"><?php echo h($loan['title']); ?></h6>
                                            <p class="card-text">
                                                <small class="text-muted">by <?php echo h($loan['author']); ?></small><br>
                                                <strong>Due Date:</strong> <?php echo formatDate($loan['due_date']); ?><br>
                                                <strong>Days Overdue:</strong> <span class="text-danger"><?php echo $days_overdue; ?> days</span><br>
                                                <strong>Fine:</strong> <span class="text-danger">$<?php echo number_format($book_fine, 2); ?></span>
                                            </p>
                                            <div class="btn-group w-100" role="group">
                                                <button class="btn btn-sm btn-success" onclick="alert('Return Book feature coming soon!')">
                                                    <i class="bi bi-arrow-return-left me-1"></i>Return
                                                </button>
                                                <button class="btn btn-sm btn-warning" onclick="alert('Pay Fine feature coming soon!')">
                                                    <i class="bi bi-credit-card me-1"></i>Pay Fine
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="text-center mt-3">
                            <button class="btn btn-primary me-2" onclick="alert('Payment feature coming soon!')">
                                <i class="bi bi-credit-card me-2"></i>Pay All Fines ($<?php echo number_format($total_fine, 2); ?>)
                            </button>
                            <button class="btn btn-outline-primary" onclick="alert('Payment feature coming soon!')">
                                <i class="bi bi-arrow-right me-2"></i>Full Payment Page
                            </button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Reading Progress Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="bi bi-bar-chart me-2"></i>Your Reading Progress</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-primary"><?php echo $books_read_this_year; ?></h4>
                                    <p class="text-muted">Books Read This Year</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-success"><?php echo h($favorite_genre); ?></h4>
                                    <p class="text-muted">Favorite Genre</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <h4 class="text-info"><?php echo count($current_loans); ?></h4>
                                    <p class="text-muted">Currently Reading</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-sm btn-outline-primary me-2" onclick="alert('Book rating feature coming soon!')">
                                <i class="bi bi-star me-1"></i>Rate Books
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="alert('Recommendations feature coming soon!')">
                                <i class="bi bi-lightbulb me-1"></i>Get Recommendations
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Member Info Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Member Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Name:</strong> <?php echo h($member['first_name'] . ' ' . $member['last_name']); ?></p>
                                <p><strong>Email:</strong> <?php echo h($member['email']); ?></p>
                                <p><strong>Phone:</strong> <?php echo h($member['phone'] ?: 'Not provided'); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Membership Date:</strong> <?php echo formatDate($member['membership_date']); ?></p>
                                <p><strong>Status:</strong>
                                    <span class="badge bg-success"><?php echo ucfirst($member['membership_status']); ?></span>
                                </p>
                                <p><strong>Outstanding Fines:</strong>
                                    <?php if ($total_fine > 0): ?>
                                        <span class="text-danger">$<?php echo number_format($total_fine, 2); ?></span>
                                    <?php else: ?>
                                        <span class="text-success">$0.00</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Loans -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-book me-2"></i>Current Loans</h5>
                        <div>
                            <span class="badge bg-primary me-2"><?php echo count($current_loans); ?></span>
                            <?php if (count($current_loans) > 0): ?>
                                <button class="btn btn-sm btn-success" onclick="alert('Return Books feature coming soon!')">
                                    <i class="bi bi-arrow-return-left me-1"></i>Return Books
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (count($current_loans) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Book</th>
                                            <th>Title</th>
                                            <th>Author</th>
                                            <th>Issue Date</th>
                                            <th>Due Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($current_loans as $loan): ?>
                                            <?php
                                            $is_overdue = strtotime($loan['due_date']) < time();
                                            $days_until_due = (strtotime($loan['due_date']) - time()) / (60 * 60 * 24);
                                            $due_soon = !$is_overdue && $days_until_due <= 3;
                                            ?>
                                            <tr class="<?php echo $is_overdue ? 'table-danger' : ($due_soon ? 'table-warning' : ''); ?>">
                                                <td>
                                                    <?php if ($loan['cover_image']): ?>
                                                        <img src="<?php echo h($loan['cover_image']); ?>" alt="Cover" class="book-cover">
                                                    <?php else: ?>
                                                        <div class="book-cover d-flex align-items-center justify-content-center bg-light">
                                                            <i class="bi bi-book fs-1 text-secondary"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <strong><?php echo h($loan['title']); ?></strong>
                                                    <br><small class="text-muted">ISBN: <?php echo h($loan['isbn']); ?></small>
                                                </td>
                                                <td><?php echo h($loan['author']); ?></td>
                                                <td><?php echo formatDate($loan['issue_date']); ?></td>
                                                <td>
                                                    <?php echo formatDate($loan['due_date']); ?>
                                                    <?php if ($is_overdue): ?>
                                                        <br><span class="badge bg-danger">Overdue</span>
                                                    <?php elseif ($due_soon): ?>
                                                        <br><span class="badge bg-warning">Due Soon</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($is_overdue): ?>
                                                        <span class="badge bg-danger">Overdue</span>
                                                    <?php elseif ($due_soon): ?>
                                                        <span class="badge bg-warning">Due Soon</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-success" title="Return Book" onclick="alert('Return Book feature coming soon!')">
                                                            <i class="bi bi-arrow-return-left"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-outline-primary" title="Renew Book" onclick="alert('Renew Book feature coming soon!')">
                                                            <i class="bi bi-arrow-clockwise"></i>
                                                        </button>
                                                        <?php if ($is_overdue && ($loan['fine'] ?? 0) > 0): ?>
                                                            <button class="btn btn-sm btn-outline-warning" title="Pay Fine" onclick="alert('Pay Fine feature coming soon!')">
                                                                <i class="bi bi-credit-card"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="bi bi-book display-1 text-muted"></i>
                                <h5 class="mt-3">No Current Loans</h5>
                                <p class="text-muted">You don't have any books checked out at the moment.</p>
                                <div class="mt-3">
                                    <a href="catalog.php" class="btn btn-primary me-2">
                                        <i class="bi bi-search me-1"></i>Browse Books
                                    </a>
                                    <button class="btn btn-outline-primary" onclick="alert('Quick Checkout feature coming soon!')">
                                        <i class="bi bi-plus-circle me-1"></i>Quick Checkout
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Reservations -->
                <?php if (count($reservations) > 0): ?>
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Book Reservations</h5>
                        <span class="badge bg-info"><?php echo count($reservations); ?></span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Author</th>
                                        <th>Reservation Date</th>
                                        <th>Expiry Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($reservations as $reservation): ?>
                                        <tr>
                                            <td><?php echo h($reservation['title']); ?></td>
                                            <td><?php echo h($reservation['author']); ?></td>
                                            <td><?php echo formatDate($reservation['reservation_date']); ?></td>
                                            <td><?php echo formatDate($reservation['expiry_date']); ?></td>
                                            <td>
                                                <?php if ($reservation['status'] === 'pending'): ?>
                                                    <span class="badge bg-secondary">Pending</span>
                                                <?php else: ?>
                                                    <span class="badge bg-success">Ready for Pickup</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Loan History -->
                <?php if (count($loan_history) > 0): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Recent Loan History</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Author</th>
                                        <th>Issue Date</th>
                                        <th>Return Date</th>
                                        <th>Fine</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($loan_history as $loan): ?>
                                        <tr>
                                            <td><?php echo h($loan['title']); ?></td>
                                            <td><?php echo h($loan['author']); ?></td>
                                            <td><?php echo formatDate($loan['issue_date']); ?></td>
                                            <td><?php echo formatDate($loan['return_date']); ?></td>
                                            <td>
                                                <?php if ($loan['fine'] > 0): ?>
                                                    <span class="text-danger">$<?php echo number_format($loan['fine'], 2); ?></span>
                                                <?php else: ?>
                                                    $0.00
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-end">
                            <button class="btn btn-sm btn-outline-primary" onclick="alert('Full loan history feature coming soon!')">View Full History</button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Enhanced Fine Payment Modal -->
    <div class="modal fade" id="payFineModal" tabindex="-1" aria-labelledby="payFineModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="payFineModalLabel">
                        <i class="bi bi-credit-card me-2"></i>Pay Outstanding Fines
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Payment Summary -->
                    <div class="alert alert-info">
                        <div class="row">
                            <div class="col-md-6">
                                <i class="bi bi-info-circle me-2"></i>
                                <strong>Total Outstanding Fine:</strong> $<?php echo number_format($total_fine, 2); ?>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-sm btn-outline-primary" onclick="alert('Payment feature coming soon!')">
                                    <i class="bi bi-arrow-right me-1"></i>Full Payment Page
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Overdue Books Breakdown -->
                    <?php if (count($current_loans) > 0): ?>
                        <?php
                        $overdue_loans = array_filter($current_loans, function($loan) {
                            return strtotime($loan['due_date']) < time();
                        });
                        ?>
                        <?php if (count($overdue_loans) > 0): ?>
                            <div class="mb-3">
                                <h6><i class="bi bi-exclamation-triangle text-danger me-2"></i>Overdue Books</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Book</th>
                                                <th>Days Overdue</th>
                                                <th>Fine</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($overdue_loans as $loan): ?>
                                                <?php
                                                $days_overdue = floor((time() - strtotime($loan['due_date'])) / (60 * 60 * 24));
                                                $book_fine = $loan['fine'] ?? ($days_overdue * 1.00);
                                                ?>
                                                <tr>
                                                    <td><?php echo h($loan['title']); ?></td>
                                                    <td><?php echo $days_overdue; ?> days</td>
                                                    <td class="text-danger">$<?php echo number_format($book_fine, 2); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Payment Feature Coming Soon -->
                    <div class="alert alert-warning">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Payment Feature Coming Soon!</strong> Online payment functionality is currently under development.
                        Please visit the library in person to pay your fines.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Renewal feature coming soon -->

    <footer class="bg-dark text-white text-center py-3 mt-5">
        <p class="mb-0">Library Management System &copy; <?php echo date('Y'); ?></p>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo url('assets/js/google-auth.js'); ?>"></script>

    <script>
        // Auto-dismiss Google sign-in alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            // Find all Google sign-in alerts
            const googleAlerts = document.querySelectorAll('.alert');

            if (googleAlerts.length > 0) {
                // Set a timeout to auto-dismiss the alerts
                setTimeout(function() {
                    googleAlerts.forEach(function(alert) {
                        // Create a Bootstrap alert instance and close it
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    });
                }, 5000); // 5 seconds
            }

            // Dashboard is now simplified - payment features coming soon
        });
    </script>

    <?php include 'includes/google_welcome.php'; ?>
</body>
</html>
