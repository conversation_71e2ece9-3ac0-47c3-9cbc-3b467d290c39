<?php
session_start();
require_once 'config/database.php';
require_once 'config/config.php';

// Connect to database
$database = new Database();
$db = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Remove Books Without Pictures</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css' rel='stylesheet'>
</head>
<body>
<div class='container mt-4'>
    <h2><i class='bi bi-images'></i> Remove Books Without Pictures</h2>";

$action = $_GET['action'] ?? 'check';

if ($action === 'remove') {
    echo "<div class='alert alert-warning'><strong>Removing books without cover images...</strong></div>";
    
    // Get books without cover images or with non-existent cover files
    $query = "SELECT id, title, author, cover_image FROM books";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $all_books = $stmt->fetchAll();
    
    $books_to_remove = [];
    $books_with_pictures = [];
    
    foreach ($all_books as $book) {
        $has_valid_cover = false;
        
        if (!empty($book['cover_image'])) {
            // Check if the cover file actually exists
            $cover_paths = [
                'uploads/covers/' . $book['cover_image'],
                'uploads/covers/' . basename($book['cover_image'])
            ];
            
            foreach ($cover_paths as $path) {
                if (file_exists($path)) {
                    $has_valid_cover = true;
                    break;
                }
            }
        }
        
        if ($has_valid_cover) {
            $books_with_pictures[] = $book;
        } else {
            $books_to_remove[] = $book;
        }
    }
    
    echo "<div class='alert alert-info'>";
    echo "<h5>📊 Analysis Results:</h5>";
    echo "<p>Books with valid cover images: <strong>" . count($books_with_pictures) . "</strong></p>";
    echo "<p>Books without cover images: <strong>" . count($books_to_remove) . "</strong></p>";
    echo "</div>";
    
    if (count($books_to_remove) > 0) {
        echo "<h4>Books to be removed:</h4>";
        echo "<div class='row mb-3'>";
        foreach ($books_to_remove as $book) {
            echo "<div class='col-md-6 mb-2'>";
            echo "<div class='card'>";
            echo "<div class='card-body'>";
            echo "<h6 class='card-title'>" . htmlspecialchars($book['title']) . "</h6>";
            echo "<p class='card-text'><small>by " . htmlspecialchars($book['author']) . "</small></p>";
            echo "<p class='card-text'><small class='text-muted'>Cover: " . ($book['cover_image'] ?: 'None') . "</small></p>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        echo "</div>";
        
        // Remove books without pictures
        $removed_count = 0;
        foreach ($books_to_remove as $book) {
            // First check if there are any active loans for this book
            $loan_check = "SELECT COUNT(*) as count FROM book_loans WHERE book_id = :book_id AND status IN ('borrowed', 'overdue')";
            $loan_stmt = $db->prepare($loan_check);
            $loan_stmt->bindParam(':book_id', $book['id']);
            $loan_stmt->execute();
            $active_loans = $loan_stmt->fetch()['count'];
            
            if ($active_loans > 0) {
                echo "<div class='alert alert-warning'>⚠️ Skipping '{$book['title']}' - has {$active_loans} active loan(s)</div>";
                continue;
            }
            
            // Remove any reservations first
            $delete_reservations = "DELETE FROM book_reservations WHERE book_id = :book_id";
            $res_stmt = $db->prepare($delete_reservations);
            $res_stmt->bindParam(':book_id', $book['id']);
            $res_stmt->execute();
            
            // Remove any completed loans
            $delete_loans = "DELETE FROM book_loans WHERE book_id = :book_id";
            $loan_stmt = $db->prepare($delete_loans);
            $loan_stmt->bindParam(':book_id', $book['id']);
            $loan_stmt->execute();
            
            // Remove the book
            $delete_book = "DELETE FROM books WHERE id = :book_id";
            $book_stmt = $db->prepare($delete_book);
            $book_stmt->bindParam(':book_id', $book['id']);
            
            if ($book_stmt->execute()) {
                echo "<div class='alert alert-success'>✅ Removed: '{$book['title']}' by {$book['author']}</div>";
                $removed_count++;
            } else {
                echo "<div class='alert alert-danger'>❌ Failed to remove: '{$book['title']}'</div>";
            }
        }
        
        echo "<div class='alert alert-success'><strong>Successfully removed {$removed_count} books without pictures!</strong></div>";
        
        // Show final count
        $final_query = "SELECT COUNT(*) as total FROM books";
        $final_stmt = $db->prepare($final_query);
        $final_stmt->execute();
        $final_count = $final_stmt->fetch()['total'];
        
        echo "<div class='alert alert-info'><h5>📊 Final Result: {$final_count} books remaining in the database</h5></div>";
        
    } else {
        echo "<div class='alert alert-success'>✅ All books already have cover images!</div>";
    }
    
    echo "<div class='mt-3'>";
    echo "<a href='catalog.php' class='btn btn-primary btn-lg me-2'>View Catalog</a>";
    echo "<a href='remove_books_without_pictures.php' class='btn btn-secondary'>Check Again</a>";
    echo "</div>";
    
} else {
    // Check current status
    echo "<h3>Current Status Check</h3>";
    
    // Get all books and check their cover images
    $query = "SELECT id, title, author, cover_image FROM books ORDER BY title";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $all_books = $stmt->fetchAll();
    
    $books_with_pictures = [];
    $books_without_pictures = [];
    
    foreach ($all_books as $book) {
        $has_valid_cover = false;
        $cover_status = 'No cover image';
        
        if (!empty($book['cover_image'])) {
            // Check if the cover file actually exists
            $cover_paths = [
                'uploads/covers/' . $book['cover_image'],
                'uploads/covers/' . basename($book['cover_image'])
            ];
            
            foreach ($cover_paths as $path) {
                if (file_exists($path)) {
                    $has_valid_cover = true;
                    $cover_status = 'Valid cover image';
                    break;
                }
            }
            
            if (!$has_valid_cover) {
                $cover_status = 'Cover file missing: ' . $book['cover_image'];
            }
        }
        
        if ($has_valid_cover) {
            $books_with_pictures[] = array_merge($book, ['cover_status' => $cover_status]);
        } else {
            $books_without_pictures[] = array_merge($book, ['cover_status' => $cover_status]);
        }
    }
    
    echo "<div class='row mb-4'>";
    echo "<div class='col-md-6'>";
    echo "<div class='card text-center'>";
    echo "<div class='card-body'>";
    echo "<h5 class='card-title text-success'>Books WITH Pictures</h5>";
    echo "<h2 class='text-success'>" . count($books_with_pictures) . "</h2>";
    echo "</div></div></div>";
    
    echo "<div class='col-md-6'>";
    echo "<div class='card text-center'>";
    echo "<div class='card-body'>";
    echo "<h5 class='card-title text-warning'>Books WITHOUT Pictures</h5>";
    echo "<h2 class='text-warning'>" . count($books_without_pictures) . "</h2>";
    echo "</div></div></div>";
    echo "</div>";
    
    if (count($books_with_pictures) > 0) {
        echo "<h4 class='text-success'>📚 Books with Valid Cover Images (" . count($books_with_pictures) . ")</h4>";
        echo "<div class='row mb-4'>";
        foreach ($books_with_pictures as $book) {
            echo "<div class='col-md-4 mb-3'>";
            echo "<div class='card h-100'>";
            
            // Show the actual cover image
            $cover_path = '';
            if (!empty($book['cover_image'])) {
                $possible_paths = [
                    'uploads/covers/' . $book['cover_image'],
                    'uploads/covers/' . basename($book['cover_image'])
                ];
                
                foreach ($possible_paths as $path) {
                    if (file_exists($path)) {
                        $cover_path = $path;
                        break;
                    }
                }
            }
            
            if ($cover_path) {
                echo "<img src='{$cover_path}' class='card-img-top' style='height: 200px; object-fit: cover;' alt='{$book['title']}'>";
            }
            
            echo "<div class='card-body'>";
            echo "<h6 class='card-title'>" . htmlspecialchars($book['title']) . "</h6>";
            echo "<p class='card-text'><small>by " . htmlspecialchars($book['author']) . "</small></p>";
            echo "<p class='card-text'><small class='text-success'>" . $book['cover_status'] . "</small></p>";
            echo "</div>";
            echo "</div>";
            echo "</div>";
        }
        echo "</div>";
    }
    
    if (count($books_without_pictures) > 0) {
        echo "<h4 class='text-warning'>📖 Books without Valid Cover Images (" . count($books_without_pictures) . ")</h4>";
        echo "<div class='table-responsive mb-4'>";
        echo "<table class='table table-striped'>";
        echo "<thead class='table-warning'>";
        echo "<tr><th>Title</th><th>Author</th><th>Cover Status</th></tr>";
        echo "</thead><tbody>";
        
        foreach ($books_without_pictures as $book) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($book['title']) . "</td>";
            echo "<td>" . htmlspecialchars($book['author']) . "</td>";
            echo "<td><small class='text-muted'>" . $book['cover_status'] . "</small></td>";
            echo "</tr>";
        }
        
        echo "</tbody></table>";
        echo "</div>";
        
        echo "<div class='alert alert-warning'>";
        echo "<h5>⚠️ Action Required</h5>";
        echo "<p>You have " . count($books_without_pictures) . " books without valid cover images.</p>";
        echo "<p>Removing these will leave you with " . count($books_with_pictures) . " books that have pictures.</p>";
        echo "</div>";
        
        echo "<div class='mt-3'>";
        echo "<a href='remove_books_without_pictures.php?action=remove' class='btn btn-warning btn-lg'>";
        echo "<i class='bi bi-trash'></i> Remove Books Without Pictures";
        echo "</a>";
        echo "</div>";
        
    } else {
        echo "<div class='alert alert-success'>";
        echo "<h5>✅ Perfect!</h5>";
        echo "<p>All " . count($books_with_pictures) . " books have valid cover images.</p>";
        echo "</div>";
    }
}

echo "</div></body></html>";
?>
